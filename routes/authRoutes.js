const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { requireAuth, checkSession, checkPermissions } = require('../middlewares/authMiddleware');

// Public routes
router.post('/login', authController.login);
router.post('/sendOtp', authController.sendOtp);
router.post('/logout', authController.logout);

// Permission checking route
router.get('/checkpermissions', checkSession, authController.checkPermissions);
router.get('/validate-session', checkSession, authController.validateSession);

// Protected routes that need user module permissions
router.post('/addUser', checkSession, checkPermissions('userModule', 'write'), authController.addUser);
router.get('/user', checkSession, checkPermissions('userModule', 'read'), authController.getUser);
router.get('/user/:id', checkSession, checkPermissions('userModule', 'read'), authController.getUserById);

// Role management routes
router.post('/addrole', checkSession, checkPermissions('userModule', 'write'), authController.addRole);
router.get('/roles', checkSession, checkPermissions('userModule', 'read'), authController.getRoles);
router.put('/roles/:id', checkSession, checkPermissions('userModule', 'update'), authController.updateRole);
router.delete('/roles/:id', checkSession, checkPermissions('userModule', 'delete'), authController.deleteRole);

module.exports = router;