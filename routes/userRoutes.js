const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { requireAuth, checkSession, checkPermissions } = require('../middlewares/authMiddleware');

// User management routes
router.get('/list', 
    checkSession, 
    checkPermissions('userModule', 'read'), 
    userController.getAllUsers
);

router.get('/view/:id', 
    checkSession, 
    checkPermissions('userModule', 'read'), 
    userController.getUserById
);

router.post('/create', 
    checkSession, 
    checkPermissions('userModule', 'write'), 
    userController.createUser
);

router.put('/update/:id', 
    checkSession, 
    checkPermissions('userModule', 'update'), 
    userController.updateUser
);

router.delete('/delete/:id', 
    checkSession, 
    checkPermissions('userModule', 'delete'), 
    userController.deleteUser
);

// In your userRoutes.js or similar
router.post('/delete-multiple', checkSession, userController.deleteUsers);

// Status management
router.patch('/toggle-status/:id', 
    checkSession, 
    checkPermissions('userModule', 'update'), 
    userController.toggleUserStatus
);

// Protected route check (keep your existing route)
router.get('/protected', 
    requireAuth, 
    userController.getProtected
);

module.exports = router;