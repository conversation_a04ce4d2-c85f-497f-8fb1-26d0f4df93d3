const express = require('express');
const router = express.Router();
const moduleController = require('../controllers/moduleController');
const { requireAuth, checkPermissions } = require('../middlewares/authMiddleware');

// Routes with middleware applied
router.get('/getmodules', requireAuth, checkPermissions('moduleModule', 'read'), moduleController.getModules);
router.get('/permissions/:roleId', requireAuth, checkPermissions('userModule', 'read'), moduleController.getRolePermissions);
router.post('/setpermissions', requireAuth, checkPermissions('userModule', 'write'), moduleController.setRolePermissions);
router.post('/createmodule', requireAuth, checkPermissions('userModule', 'write'), moduleController.createModule);
router.delete('/deletemodule/:id', requireAuth, checkPermissions('userModule', 'delete'), moduleController.deleteModule);
router.put('/updatemodulename/:id', requireAuth, checkPermissions('userModule', 'write'), moduleController.updateModuleName);
router.put('/updaterolemodulepermissions', requireAuth, checkPermissions('userModule', 'write'), moduleController.updateRoleModulePermissions); // New route for updating role module permissions

// test
module.exports = router;