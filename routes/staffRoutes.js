const express = require('express');
const router = express.Router();
const staffController = require('../controllers/staffController');
const { checkSession } = require('../middlewares/authMiddleware');
const { uploadProfilePicture, handleUploadError } = require('../middlewares/fileUpload');

// Staff CRUD Operations
router.post('/create', 
    checkSession, 
    uploadProfilePicture,
    handleUploadError,
    staffController.addStaff
);

router.get('/list', 
    checkSession, 
    staffController.getAllStaff
);

router.get('/view/:id', 
    checkSession, 
    staffController.getStaffById
);

router.put('/update/:id', 
    checkSession,
    uploadProfilePicture,  // Middleware for handling file uploads
    handleUploadError,
    staffController.updateStaff
  );
router.put('/update-profile-picture/:id', 
    checkSession, 
    uploadProfilePicture,
    handleUploadError,
    staffController.updateProfilePicture
);

router.delete('/delete/:id', 
    checkSession, 
    staffController.deleteStaff
);

router.post('/delete-multiple', 
    checkSession, 
    staffController.deleteStaffs
);

router.patch('/toggle-status/:id', 
    checkSession, 
    staffController.toggleStaffStatus
);

// Sibling Management Routes
router.post('/:staffId/siblings', 
    checkSession,
    staffController.addSibling
);

router.get('/:staffId/siblings', 
    checkSession,
    staffController.getStaffById  // This will include siblings data
);

router.put('/siblings/:id', 
    checkSession,
    staffController.updateSibling
);

router.delete('/siblings/:id', 
    checkSession,
    staffController.deleteSibling
);

// Advanced Search Routes
router.post('/search', 
    checkSession,
    (req, res) => {
        staffController.searchStaff(req, res);
    }
);

// Statistics and Reporting Routes
router.get('/stats/overview', 
    checkSession,
    (req, res) => {
        staffController.getStaffStats(req, res);
    }
);

router.get('/stats/department', 
    checkSession,
    (req, res) => {
        staffController.getDepartmentWiseCount(req, res);
    }
);

// Batch Operations
router.post('/update-multiple', 
    checkSession,
    (req, res) => {
        staffController.updateMultipleStaff(req, res);
    }
);

// Export/Import Routes
router.get('/export', 
    checkSession,
    (req, res) => {
        staffController.exportStaffData(req, res);
    }
);

router.post('/import', 
    checkSession,
    uploadProfilePicture,  // You might want to create a separate middleware for file uploads
    handleUploadError,
    (req, res) => {
        staffController.importStaffData(req, res);
    }
);

module.exports = router;