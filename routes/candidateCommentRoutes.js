// routes/candidateCommentRoutes.js

const express = require('express');
const router = express.Router();
const candidateCommentController = require('../controllers/interview/candidateCommentController');
const { checkSession } = require('../middlewares/authMiddleware');

// All routes should be protected by the session check
router.use(checkSession);

// Add or update a comment
router.post('/', candidateCommentController.addOrUpdateComment);

// Get all comments for a candidate
router.get('/:candidate_id', candidateCommentController.getCandidateComments);

// Get comments for a specific field
router.get('/:candidate_id/:field_identifier', candidateCommentController.getFieldComments);

// Mark a comment as read
router.patch('/:comment_id/read', candidateCommentController.markCommentAsRead);

// Delete a comment
router.delete('/:comment_id', candidateCommentController.deleteComment);

module.exports = router;