// routes/publicRoutes.js
const express = require('express');
const router = express.Router();
const publicController = require('../controllers/publicController');
const publicMasterController = require('../controllers/interview/publicMasterController');
const { uploadCandidateFiles, handleUploadError } = require('../middlewares/fileUpload');

// Database connection test endpoint
router.get('/db-test', publicMasterController.testDatabase);

// Public candidate registration routes
router.post('/candidate-registration/:token', 
    uploadCandidateFiles,
    handleUploadError,
    publicController.registerCandidate
);
router.get('/validate-token/:token', publicController.validateToken);
router.get('/open-positions', publicController.getOpenPositions);

// Public master data routes - make sure these match what your Angular service expects
router.get('/master/genders', publicMasterController.getGenders);
router.get('/master/blood-groups', publicMasterController.getBloodGroups);
router.get('/master/religions', publicMasterController.getReligions);
router.get('/master/communities', publicMasterController.getCommunities);
router.get('/master/states', publicMasterController.getStates);
router.get('/master/states/:stateId/cities', publicMasterController.getCitiesByState);
router.get('/master/positions', publicMasterController.getOpenPositions);
router.get('/master/positions/:id', publicMasterController.getPositionById);
router.get('/master/departments', publicMasterController.getDepartments);
router.get('/master/designations', publicMasterController.getDesignations);
router.get('/master/employment-types', publicMasterController.getEmploymentTypes);

// Debug/Test endpoint
router.get('/debug', (req, res) => {
  res.json({
    success: true,
    message: 'Public API routes are working',
    timestamp: new Date().toISOString(),
    endpoints: {
      registration: [
        '/candidate-registration/:token',
        '/validate-token/:token',
        '/open-positions'
      ],
      masterData: [
        '/master/genders',
        '/master/blood-groups',
        '/master/religions',
        '/master/communities',
        '/master/states',
        '/master/states/:stateId/cities',
        '/master/positions',
        '/master/positions/:id',
        '/master/departments',
        '/master/designations',
        '/master/employment-types'
      ],
      diagnostics: [
        '/db-test',
        '/debug'
      ]
    }
  });
});

// Fallback route to catch all other requests
router.all('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route not found: ${req.method} ${req.originalUrl}`,
    availableRoutes: [
      '/candidate-registration/:token',
      '/validate-token/:token',
      '/open-positions',
      '/master/genders',
      '/master/blood-groups',
      '/master/religions',
      '/master/communities',
      '/master/states',
      '/master/states/:stateId/cities',
      '/master/positions',
      '/master/positions/:id',
      '/master/departments',
      '/master/designations',
      '/master/employment-types',
      '/db-test',
      '/debug'
    ]
  });
});

module.exports = router;