const express = require('express');
const router = express.Router();
const masterController = require('../controllers/masterController');
const { checkSession } = require('../middlewares/authMiddleware');
const { requireAuth, checkPermissions } = require('../middlewares/authMiddleware');
const publicMasterController = require('../controllers/interview/publicMasterController');

// State routes
// State routes
router.post('/state', requireAuth, checkPermissions('masterModule', 'write'), masterController.addState);
router.get('/states', requireAuth, checkPermissions('masterModule', 'read'), masterController.getAllStates);
router.get('/state/:id', requireAuth, checkPermissions('masterModule', 'read'), masterController.getStateById);
router.put('/state/:id', requireAuth, checkPermissions('masterModule', 'update'), masterController.updateState);
router.delete('/state/:id', requireAuth, checkPermissions('masterModule', 'delete'), masterController.deleteState);


// City routes
router.post('/city', checkSession, masterController.addCity);
router.get('/cities', checkSession, masterController.getAllCities);
router.get('/city/:id', checkSession, masterController.getCityById);
router.put('/city/:id', checkSession, masterController.updateCity);
router.delete('/city/:id', checkSession, masterController.deleteCity);
router.post('/deleteCities', checkSession, masterController.deleteCities);
router.get('/cities/state/:stateId', checkSession, masterController.getCitiesByStateId);

// Store routes
router.post('/store', checkSession, masterController.addStore);
router.get('/stores', checkSession, masterController.getAllStores);
router.get('/store/:id', checkSession, masterController.getStoreById);
router.put('/store/:id', checkSession, masterController.updateStore);
router.delete('/store/:id', checkSession, masterController.deleteStore);
router.post('/deleteStores', checkSession, masterController.deleteStores);
router.patch('/store/:id/toggle-status', checkSession, masterController.toggleStoreStatus);
router.get('/managers', checkSession, masterController.getAllManagers);

// Religion routes
router.post('/religion', checkSession, masterController.addReligion);
router.get('/religions', checkSession, masterController.getAllReligions);
router.get('/religion/:id', checkSession, masterController.getReligionById);
router.put('/religion/:id', checkSession, masterController.updateReligion);
router.delete('/religion/:id', checkSession, masterController.deleteReligion);
router.post('/deleteReligions', checkSession, masterController.deleteReligions);

// Community routes
router.post('/community', checkSession, masterController.addCommunity);
router.get('/communities', checkSession, masterController.getAllCommunities);
router.get('/community/:id', checkSession, masterController.getCommunityById);
router.put('/community/:id', checkSession, masterController.updateCommunity);
router.delete('/community/:id', checkSession, masterController.deleteCommunity);
router.post('/deleteCommunities', checkSession, masterController.deleteCommunities);


// Blood Group routes
router.post('/bloodgroup', checkSession, masterController.addBloodGroup);
router.get('/bloodgroups', checkSession, masterController.getAllBloodGroups);
router.get('/bloodgroup/:id', checkSession, masterController.getBloodGroupById);
router.put('/bloodgroup/:id', checkSession, masterController.updateBloodGroup);
router.delete('/bloodgroup/:id', checkSession, masterController.deleteBloodGroup);
router.post('/deleteBloodGroups', checkSession, masterController.deleteBloodGroups);

// Designation routes
router.post('/designation', checkSession, masterController.addDesignation);
router.get('/designations', checkSession, masterController.getAllDesignations);
router.get('/designation/:id', checkSession, masterController.getDesignationById);
router.put('/designation/:id', checkSession, masterController.updateDesignation);
router.delete('/designation/:id', checkSession, masterController.deleteDesignation);
router.post('/deleteDesignations', checkSession, masterController.deleteDesignations);


// Department routes
router.post('/department', checkSession, masterController.addDepartment);
router.get('/departments', checkSession, masterController.getAllDepartments);
router.get('/department/:id', checkSession, masterController.getDepartmentById);
router.put('/department/:id', checkSession, masterController.updateDepartment);
router.delete('/department/:id', checkSession, masterController.deleteDepartment);
router.post('/deleteDepartments', checkSession, masterController.deleteDepartments);
router.patch('/department/:id/toggle-status', checkSession, masterController.toggleDepartmentStatus);


// Employment Type routes
router.post('/employmentType', checkSession, masterController.addEmploymentType);
router.get('/employmentTypes', checkSession, masterController.getAllEmploymentTypes);
router.get('/employmentType/:id', checkSession, masterController.getEmploymentTypeById);
router.put('/employmentType/:id', checkSession, masterController.updateEmploymentType);
router.delete('/employmentType/:id', checkSession, masterController.deleteEmploymentType);
router.post('/deleteEmploymentTypes', checkSession, masterController.deleteEmploymentTypes);
router.patch('/employmentType/:id/toggle-status', checkSession, masterController.toggleEmploymentTypeStatus);

// Gender routes
router.post('/gender', checkSession, masterController.addGender);
router.get('/genders', checkSession, masterController.getAllGenders);
router.get('/gender/:id', checkSession, masterController.getGenderById);
router.put('/gender/:id', checkSession, masterController.updateGender);
router.delete('/gender/:id', checkSession, masterController.deleteGender);
router.post('/deleteGenders', checkSession, masterController.deleteGenders);
router.patch('/gender/:id/toggle-status', checkSession, masterController.toggleGenderStatus);


//staffstore linking
router.post('/staff-store', 
    checkSession, 
    masterController.assignStaffToStore
);

router.post('/staff-store/bulk', 
    checkSession, 
    masterController.bulkAssignStaffToStore
);

// Get all staff assigned to a specific store
// Add this new route
router.get('/store/:storeId/staff', checkSession, masterController.getStoreStaff);

// Get all stores assigned to a specific staff member
router.get('/staff/:staff_id/stores', 
    checkSession, 
    masterController.getStaffStores
);

router.get('/unassigned-staff', checkSession, masterController.getUnassignedStaff);



// Remove a staff member from a store
router.delete('/staff-store/:staff_id/:store_id', 
    checkSession, 
    masterController.removeStaffFromStore
);

// Update the active status of a staff-store mapping
router.patch('/staff-store/:mapping_id/status', 
    checkSession, 
    masterController.updateStaffStoreStatus
);

router.post('/staff-store/bulk-delete', checkSession, masterController.bulkRemoveStaffFromStore
);

router.get('/public/master/genders', publicMasterController.getGenders);
router.get('/public/master/blood-groups', publicMasterController.getBloodGroups);
router.get('/public/master/religions', publicMasterController.getReligions);
router.get('/public/master/communities', publicMasterController.getCommunities);
router.get('/public/master/states', publicMasterController.getStates);
router.get('/public/master/states/:stateId/cities', publicMasterController.getCitiesByState);



module.exports = router; 