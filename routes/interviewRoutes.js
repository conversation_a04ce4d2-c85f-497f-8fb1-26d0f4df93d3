// routes/interviewRoutes.js

const express = require('express');
const router = express.Router();
const interviewController = require('../controllers/interviewController');
const registrationTokenController = require('../controllers/registrationTokenController');
const { checkSession, checkPermissions } = require('../middlewares/authMiddleware');
const { uploadCandidateFiles, handleUploadError } = require('../middlewares/fileUpload');

// Position Management Routes
router.post('/position',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.addPosition
);

router.get('/positions',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getAllPositions
);

router.get('/position/:id',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getPositionById
);

router.put('/position/:id',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updatePosition
);

router.delete('/position/:id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deletePosition
);

router.post('/positions/delete',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deletePositions
);

router.patch('/position/:id/toggle-status',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.togglePositionStatus
);

router.patch('/position/:id/vacancies',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateVacancies
);

router.get('/positions/department/:departmentId',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getPositionsByDepartment
);

router.get('/positions/open',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getOpenPositions
);

// Candidate Management Routes
router.post('/candidate',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   uploadCandidateFiles,
   handleUploadError,
   interviewController.addCandidate
);

router.get('/candidates',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getAllCandidates
);

router.get('/candidate/:id',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getCandidateById
);

router.put('/candidate/:id',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   uploadCandidateFiles,
   handleUploadError,
   interviewController.updateCandidate
);

router.delete('/candidate/:id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deleteCandidate
);

router.post('/candidates/delete',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deleteCandidates
);

router.patch('/candidate/:id/status',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateCandidateStatus
);

// Candidate Comment Routes
router.post('/comment',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.addOrUpdateComment
);

router.get('/comments/candidate/:candidate_id',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getCandidateComments
);

router.get('/comments/candidate/:candidate_id/field/:field_identifier',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getFieldComments
);

router.patch('/comment/:comment_id/read',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.markCommentAsRead
);

router.delete('/comment/:comment_id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deleteComment
);

// Candidate Sibling Routes (New)
router.post('/candidate/:candidateId/siblings',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.addCandidateSibling
);

router.put('/candidate-sibling/:id',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateCandidateSibling
);

router.delete('/candidate-sibling/:id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deleteCandidateSibling
);

// Candidate Position Mapping Routes
router.post('/candidate-position',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.assignCandidateToPosition
);

router.get('/candidate/:candidateId/positions',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getCandidatePositions
);

router.get('/position/:positionId/candidates',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getPositionCandidates
);

router.patch('/candidate/:candidateId/position/:positionId/status',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateCandidatePositionStatus
);

router.delete('/candidate/:candidateId/position/:positionId',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.removeCandidateFromPosition
);

// Interview Session Routes
router.post('/interview-session',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.addInterviewSession
);

router.get('/interview-sessions/:candidateId/:positionId',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getInterviewSessions
);

router.get('/interview-session/:id',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getInterviewSessionById
);

router.put('/interview-session/:id',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateInterviewSession
);

router.delete('/interview-session/:id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   interviewController.deleteInterviewSession
);

router.get('/interview-sessions',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getAllInterviewSessions
);

// Candidate Decision Routes
router.post('/candidate-decision',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   interviewController.addCandidateDecision
);

router.get('/candidate-decision/:candidateId/:positionId',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getCandidateDecision
);

router.put('/candidate-decision/:id',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.updateCandidateDecision
);

router.patch('/candidate-decision/:decisionId/staff',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   interviewController.setStaffForCandidate
);

// Search and Reporting Routes
router.post('/candidates/search',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.searchCandidates
);

// Dashboard Routes
router.get('/dashboard/stats',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getDashboardStats
);

router.get('/candidates/recent',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getRecentCandidates
);

router.get('/interview-sessions/upcoming',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   interviewController.getUpcomingSessions
);

// Registration Token Management Routes
router.post('/registration-token',
   checkSession,
   checkPermissions('interviewModule', 'write'),
   registrationTokenController.generateToken
);

router.get('/registration-tokens',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   registrationTokenController.getAllTokens
);

router.get('/my-registration-tokens',
   checkSession,
   checkPermissions('interviewModule', 'read'),
   registrationTokenController.getMyTokens
);

router.patch('/registration-token/:token/invalidate',
   checkSession,
   checkPermissions('interviewModule', 'update'),
   registrationTokenController.invalidateToken
);

router.delete('/registration-token/:id',
   checkSession,
   checkPermissions('interviewModule', 'delete'),
   registrationTokenController.deleteToken
);

module.exports = router;