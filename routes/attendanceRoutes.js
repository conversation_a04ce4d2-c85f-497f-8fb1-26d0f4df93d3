const express = require('express');
const router = express.Router();
const attendanceController = require('../controllers/attendanceController');
const { checkSession } = require('../middlewares/authMiddleware');

// Mark attendance for a store
router.post('/mark', 
    checkSession,
    attendanceController.markAttendance
);

// Get attendance for a store on a specific date
router.get('/store/:store_id/date/:date', 
    checkSession,
    attendanceController.getStoreAttendance
);

// Get attendance history for a staff member
router.get('/staff/:staff_id/history', 
    checkSession,
    attendanceController.getStaffHistory
);

// Get attendance summary for a store
router.get('/store/:store_id/summary', 
    checkSession,
    attendanceController.getStoreSummary
);

module.exports = router;