// models/interviewModel.js

const db = require('../config/database');

const Interview = {
    // Table creation methods
    createPositionsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS positions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                department_id INT NOT NULL,
                required_skills TEXT,
                required_qualifications TEXT,
                min_experience DECIMAL(5,2),
                max_experience DECIMAL(5,2),
                vacancies INT DEFAULT 1,
                priority ENUM('High', 'Medium', 'Low') DEFAULT 'Medium',
                status ENUM('Open', 'Filled', 'On Hold') DEFAULT 'Open',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (department_id) REFERENCES departments(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOR<PERSON><PERSON><PERSON> KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating positions table:', err);
            } else {
                console.log('Positions table ensured');
            }
        });
    },

    createCandidatesTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                firstName VARCHAR(255) NOT NULL,
                lastName VARCHAR(255) NOT NULL,
                dateOfBirth DATE DEFAULT NULL,
                genderId INT DEFAULT NULL,
                profilePicture VARCHAR(255) DEFAULT NULL,
                email VARCHAR(255) NOT NULL,
                phoneNumber VARCHAR(20) NOT NULL,
                alternatePhone VARCHAR(20) DEFAULT NULL,
                emergencyContactName VARCHAR(255) DEFAULT NULL,
                emergencyContactNumber VARCHAR(20) DEFAULT NULL,
                address TEXT,
                permanentAddress TEXT,
                city VARCHAR(255) DEFAULT NULL,
                state VARCHAR(255) DEFAULT NULL,
                postalCode VARCHAR(20) DEFAULT NULL,
                employeeId VARCHAR(50) DEFAULT NULL,
                hireDate DATE DEFAULT NULL,
                departmentId INT DEFAULT NULL,
                designationId INT DEFAULT NULL,
                employmentTypeId INT DEFAULT NULL,
                educationLevel VARCHAR(255) DEFAULT NULL,
                degrees VARCHAR(255) DEFAULT NULL,
                university VARCHAR(255) DEFAULT NULL,
                yearOfPassing INT DEFAULT NULL,
                specialization VARCHAR(255) DEFAULT NULL,
                additionalCertifications TEXT,
                totalExperience DECIMAL(5,2) DEFAULT NULL,
                currentOrganization VARCHAR(255) DEFAULT NULL,
                currentDesignation VARCHAR(255) DEFAULT NULL,
                currentSalary DECIMAL(12,2) DEFAULT NULL,
                expectedSalary DECIMAL(12,2) DEFAULT NULL,
                noticePeriod INT DEFAULT NULL,
                reasonForChange TEXT,
                skills TEXT,
                salary DECIMAL(10,2) DEFAULT NULL,
                bloodGroupId INT DEFAULT NULL,
                religion VARCHAR(255) DEFAULT NULL,
                community VARCHAR(255) DEFAULT NULL,
                idProofType VARCHAR(50) DEFAULT NULL,
                idProofNumber VARCHAR(100) DEFAULT NULL,
                idProof VARCHAR(255) DEFAULT NULL,
                father_name VARCHAR(255) DEFAULT NULL,
                father_occupation VARCHAR(255) DEFAULT NULL,
                father_contact VARCHAR(20) DEFAULT NULL,
                father_status ENUM('Living', 'Deceased') DEFAULT 'Living',
                mother_name VARCHAR(255) DEFAULT NULL,
                mother_occupation VARCHAR(255) DEFAULT NULL,
                mother_contact VARCHAR(20) DEFAULT NULL,
                mother_status ENUM('Living', 'Deceased') DEFAULT 'Living',
                family_address TEXT,
                referenceContacts TEXT,
                resumeFile VARCHAR(255) DEFAULT NULL,
                portfolioLinks TEXT,
                status ENUM('New', 'In Process', 'Selected', 'Rejected') DEFAULT 'New',
                isActive TINYINT(1) DEFAULT 1,
                employmentStatus ENUM('Active', 'On Leave', 'Terminated') DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT DEFAULT NULL,
                UNIQUE KEY (email),
                FOREIGN KEY (genderId) REFERENCES genders(id),
                FOREIGN KEY (bloodGroupId) REFERENCES blood_groups(id),
                FOREIGN KEY (departmentId) REFERENCES departments(id),
                FOREIGN KEY (designationId) REFERENCES designations(id),
                FOREIGN KEY (employmentTypeId) REFERENCES employment_types(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidates table:', err);
            } else {
                console.log('Candidates table ensured');
            }
        });
    },

    createCandidatePositionsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidate_positions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                position_id INT NOT NULL,
                application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('Applied', 'In Process', 'Selected', 'Rejected') DEFAULT 'Applied',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIGN KEY (position_id) REFERENCES positions(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                UNIQUE KEY unique_candidate_position (candidate_id, position_id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidate_positions table:', err);
            } else {
                console.log('Candidate positions table ensured');
            }
        });
    },

    createCandidateSiblingsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidate_siblings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                date_of_birth DATE NOT NULL,
                gender VARCHAR(50) DEFAULT NULL,
                occupation VARCHAR(255) DEFAULT NULL,
                marital_status VARCHAR(50) DEFAULT NULL,
                contact VARCHAR(20) DEFAULT NULL,
                status ENUM('Living', 'Deceased') DEFAULT 'Living',
                is_emergency_contact BOOLEAN DEFAULT FALSE,
                additional_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT DEFAULT NULL,
                FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidate_siblings table:', err);
            } else {
                console.log('Candidate siblings table ensured');
            }
        });
    },

    createInterviewSession: (sessionData, callback) => {
        // We don't need to check for outcome or set defaults
        const query = 'INSERT INTO interview_sessions SET ?';
        db.query(query, sessionData, callback);
    },

    createInterviewSessionsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS interview_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                position_id INT NOT NULL,
                round_title VARCHAR(255) NOT NULL,
                interview_date DATETIME NOT NULL,
                interview_mode ENUM('In-person', 'Video', 'Phone') DEFAULT 'In-person',
                interviewer_id INT NOT NULL,
                comments TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                updated_by INT DEFAULT NULL,
                FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIGN KEY (position_id) REFERENCES positions(id),
                FOREIGN KEY (interviewer_id) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating interview_sessions table:', err);
            } else {
                console.log('Interview sessions table ensured');
            }
        });
    },

    createCandidateDecisionsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidate_decisions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                position_id INT NOT NULL,
                decision ENUM('Selected', 'Rejected') NOT NULL,
                decision_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                remarks TEXT,
                approved_by INT NOT NULL,
                staff_id INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIGN KEY (position_id) REFERENCES positions(id),
                FOREIGN KEY (approved_by) REFERENCES users(id),
                FOREIGN KEY (staff_id) REFERENCES staff(id),
                UNIQUE KEY unique_candidate_decision (candidate_id, position_id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidate_decisions table:', err);
            } else {
                console.log('Candidate decisions table ensured');
            }
        });
    },

    // Position CRUD operations
    createPosition: (positionData, callback) => {
        const query = 'INSERT INTO positions SET ?';
        db.query(query, positionData, callback);
    },

    // Updated getAllCandidates method with field transformation
   // Update this function in interviewModel.js

   getInterviewSessions: (candidateId, positionId, callback) => {
    const query = `
        SELECT s.*, 
               c.firstName as first_name, 
               c.lastName as last_name,
               p.title as position_title,
               u.username as interviewer_name
        FROM interview_sessions s
        JOIN candidates c ON s.candidate_id = c.id
        JOIN positions p ON s.position_id = p.id
        LEFT JOIN users u ON s.interviewer_id = u.id
        WHERE s.candidate_id = ? AND s.position_id = ?
        ORDER BY s.interview_date DESC
    `;
    db.query(query, [candidateId, positionId], callback);
},

getInterviewSessionById: (id, callback) => {
    const query = `
        SELECT s.*, 
               c.firstName as first_name, 
               c.lastName as last_name,
               c.id as candidate_id,
               p.id as position_id,
               p.title as position_title,
               u.username as interviewer_name,
               uu.username as updated_by_name
        FROM interview_sessions s
        JOIN candidates c ON s.candidate_id = c.id
        JOIN positions p ON s.position_id = p.id
        LEFT JOIN users u ON s.interviewer_id = u.id
        LEFT JOIN users uu ON s.updated_by = uu.id
        WHERE s.id = ?
    `;
    db.query(query, [id], callback);
},


getAllCandidates: (callback) => {
    try {
      const query = `
        SELECT 
          c.id, 
          c.firstName, 
          c.lastName, 
          c.email, 
          c.phoneNumber,
          c.totalExperience,
          c.status,
          g.name AS gender_name,
          d.name AS department_name,
          (SELECT COUNT(*) FROM candidate_positions WHERE candidate_id = c.id) AS position_count
        FROM candidates c
        LEFT JOIN genders g ON c.genderId = g.id
        LEFT JOIN departments d ON c.departmentId = d.id
        ORDER BY c.created_at DESC
      `;
      
      db.query(query, (err, results) => {
        if (err) {
          console.error('Error in getAllCandidates:', err);
          callback(err, []);
          return;
        }
        
        // Transform field names to match what frontend expects
        const transformedResults = results.map(candidate => ({
          id: candidate.id,
          first_name: candidate.firstName || '',
          last_name: candidate.lastName || '',
          email: candidate.email || '',
          mobile_number: candidate.phoneNumber || '',
          total_experience: candidate.totalExperience || 0,
          status: candidate.status || 'New',
          gender_name: candidate.gender_name || '',
          department_name: candidate.department_name || '',
          position_count: candidate.position_count || 0
        }));
        
        callback(null, transformedResults);
      });
    } catch (e) {
      console.error('Exception in getAllCandidates:', e);
      callback(e, []);
    }
  },
    getAllPositions: (callback) => {
        const query = `
            SELECT p.*, 
                   d.name AS department_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username,
                   (
                       SELECT COUNT(*) 
                       FROM candidate_positions 
                       WHERE position_id = p.id AND status IN ('Applied', 'In Process')
                   ) AS active_candidates
            FROM positions p
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN users cu ON p.created_by = cu.id
            LEFT JOIN users uu ON p.updated_by = uu.id
            ORDER BY p.created_at DESC
        `;
        db.query(query, callback);
    },

    // Candidate Methods
    createCandidate: (candidateData, callback) => {
        const query = 'INSERT INTO candidates SET ?';
        db.query(query, candidateData, callback);
    },

    // Candidate Sibling Methods
    createCandidateSibling: (siblingData, callback) => {
        const query = 'INSERT INTO candidate_siblings SET ?';
        db.query(query, siblingData, callback);
    },

    getCandidateSiblings: (candidateId, callback) => {
        const query = `
            SELECT s.*, 
                   TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age,
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM candidate_siblings s
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.candidate_id = ?
            ORDER BY s.date_of_birth
        `;
        db.query(query, [candidateId], callback);
    },

    updateCandidateSibling: (id, siblingData, callback) => {
        const query = 'UPDATE candidate_siblings SET ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [siblingData, id], callback);
    },

    deleteCandidateSibling: (id, callback) => {
        const query = 'DELETE FROM candidate_siblings WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCandidateSiblings: (ids, callback) => {
        const query = 'DELETE FROM candidate_siblings WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // New method to update candidate status
    updateCandidateStatus: (id, status, updated_by, callback) => {
        const query = `
            UPDATE candidates 
            SET status = ?,
                updated_by = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        db.query(query, [status, updated_by, id], callback);
    },

    // Dashboard Methods
    getDashboardStats: (callback) => {
        const query = `
            SELECT 
                (SELECT COUNT(*) FROM candidates) AS totalCandidates,
                (SELECT COUNT(*) FROM positions WHERE status = 'Open') AS openPositions,
                (SELECT COUNT(*) FROM candidates WHERE status = 'In Process') AS inProcessCandidates,
                (SELECT COUNT(*) FROM candidates WHERE status = 'Selected') AS selectedCandidates,
                (SELECT COUNT(*) FROM interview_sessions WHERE DATE(interview_date) = CURDATE()) AS todayInterviews,
                (SELECT SUM(vacancies) FROM positions WHERE status = 'Open') AS totalVacancies,
                (SELECT COUNT(*) FROM interview_sessions) AS totalInterviews,
                (SELECT COUNT(*) FROM candidate_decisions WHERE decision = 'Selected') AS hiredCandidates,
                (SELECT COUNT(*) FROM candidate_decisions WHERE decision = 'Rejected') AS rejectedCandidates
        `;
        
        db.query(query, callback);
    },

    getRecentCandidates: (limit, callback) => {
        const query = `
            SELECT c.id, c.firstName, c.lastName, c.email, c.status,
                   cp.position_id,
                   p.title AS position_title
            FROM candidates c
            LEFT JOIN candidate_positions cp ON c.id = cp.candidate_id
            LEFT JOIN positions p ON cp.position_id = p.id
            ORDER BY c.created_at DESC
            LIMIT ?
        `;
        
        db.query(query, [limit], (err, results) => {
            if (err) {
                console.error('Database error in getRecentCandidates:', err);
            }
            callback(err, results || []);
        });
    },

    getUpcomingSessions: (limit, callback) => {
        const query = `
            SELECT s.id, s.interview_date, s.round_title, s.interview_mode,
                   c.id AS candidate_id, c.firstName, c.lastName,
                   p.id AS position_id, p.title AS position_title,
                   u.username AS interviewer_name
            FROM interview_sessions s
            JOIN candidates c ON s.candidate_id = c.id
            JOIN positions p ON s.position_id = p.id
            LEFT JOIN users u ON s.interviewer_id = u.id
            WHERE s.interview_date >= CURDATE()
            ORDER BY s.interview_date
            LIMIT ?
        `;
        
        db.query(query, [limit], (err, results) => {
            if (err) {
                console.error('Database error in getUpcomingSessions:', err);
            }
            callback(err, results || []);
        });
    },

    assignCandidateToPosition: (mappingData, callback) => {
        const query = 'INSERT INTO candidate_positions SET ?';
        db.query(query, mappingData, callback);
    },

    getOpenPositions: (callback) => {
        const query = `
            SELECT p.*, 
                   d.name AS department_name
            FROM positions p
            LEFT JOIN departments d ON p.department_id = d.id
            WHERE p.status = 'Open'
            ORDER BY p.created_at DESC
        `;
        
        db.query(query, (err, results) => {
            if (err) {
                console.error('Error in getOpenPositions:', err);
            }
            callback(err, results || []);
        });
    },

    // Updated searchCandidates method with field transformation
    searchCandidates: (searchTerms, callback) => {
        let query = `
            SELECT c.*,
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name,
                   (
                       SELECT COUNT(*) 
                       FROM candidate_positions 
                       WHERE candidate_id = c.id
                   ) AS position_count,
                   (
                       SELECT COUNT(*) 
                       FROM interview_sessions 
                       WHERE candidate_id = c.id
                   ) AS interview_count
            FROM candidates c
            LEFT JOIN genders g ON c.genderId = g.id
            LEFT JOIN departments d ON c.departmentId = d.id
            LEFT JOIN designations ds ON c.designationId = ds.id
            LEFT JOIN employment_types et ON c.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON c.bloodGroupId = bg.id
            WHERE 1=1
        `;
        
        const queryParams = [];
        
        // Add search conditions
        if (searchTerms.name) {
            query += ` AND (c.firstName LIKE ? OR c.lastName LIKE ? OR CONCAT(c.firstName, ' ', c.lastName) LIKE ?)`;
            const searchPattern = `%${searchTerms.name}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern);
        }
        
        if (searchTerms.email) {
            query += ` AND c.email LIKE ?`;
            queryParams.push(`%${searchTerms.email}%`);
        }
        
        if (searchTerms.mobile) {
            query += ` AND c.phoneNumber LIKE ?`;
            queryParams.push(`%${searchTerms.mobile}%`);
        }
        
        if (searchTerms.status) {
            query += ` AND c.status = ?`;
            queryParams.push(searchTerms.status);
        }
        
        if (searchTerms.min_experience) {
            query += ` AND c.totalExperience >= ?`;
            queryParams.push(searchTerms.min_experience);
        }
        
        if (searchTerms.skills) {
            query += ` AND c.skills LIKE ?`;
            queryParams.push(`%${searchTerms.skills}%`);
        }
        
        if (searchTerms.position_id) {
            query += ` AND c.id IN (SELECT candidate_id FROM candidate_positions WHERE position_id = ?)`;
            queryParams.push(searchTerms.position_id);
        }
        
        query += ` ORDER BY c.created_at DESC`;
        
        db.query(query, queryParams, (err, results) => {
            if (err) {
                callback(err, []);
                return;
            }
            
            // Transform results to match frontend field names
            const transformedResults = results.map(candidate => ({
                id: candidate.id,
                first_name: candidate.firstName,
                last_name: candidate.lastName,
                email: candidate.email,
                mobile_number: candidate.phoneNumber,
                total_experience: candidate.totalExperience,
                status: candidate.status,
                gender_name: candidate.gender_name,
                department_name: candidate.department_name,
                position_count: candidate.position_count,
                interview_count: candidate.interview_count
            }));
            
            callback(null, transformedResults);
        });
    },

    getAllInterviewSessions: (callback) => {
        const query = `
            SELECT s.*, 
                   c.firstName as first_name, 
                   c.lastName as last_name,
                   c.id as candidate_id,
                   p.id as position_id,
                   p.title as position_title,
                   u.username as interviewer_name
            FROM interview_sessions s
            JOIN candidates c ON s.candidate_id = c.id
            JOIN positions p ON s.position_id = p.id
            LEFT JOIN users u ON s.interviewer_id = u.id
            ORDER BY s.interview_date DESC
        `;
        
        db.query(query, callback);
    },

    getPositionById: (id, callback) => {
        const query = `
            SELECT p.*, 
                   d.name AS department_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username,
                   (
                       SELECT COUNT(*) 
                       FROM candidate_positions 
                       WHERE position_id = p.id AND status IN ('Applied', 'In Process')
                   ) AS active_candidates
            FROM positions p
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN users cu ON p.created_by = cu.id
            LEFT JOIN users uu ON p.updated_by = uu.id
            WHERE p.id = ?
        `;
        
        db.query(query, [id], callback);
    },
    

    getPositionStats: (callback) => {
        const query = `
            SELECT 
                status,
                COUNT(*) as count,
                SUM(vacancies) as total_vacancies
            FROM positions
            GROUP BY status
        `;
        db.query(query, callback);
    },

    getHiringPipelineStats: (callback) => {
        const query = `
            SELECT 
                'Applied' as stage,
                COUNT(*) as count
            FROM candidate_positions
            WHERE status = 'Applied'
            UNION
            SELECT 
                'In Process' as stage,
                COUNT(*) as count
            FROM candidate_positions
            WHERE status = 'In Process'
            UNION
            SELECT 
                'Selected' as stage,
                COUNT(*) as count
            FROM candidate_positions
            WHERE status = 'Selected'
            UNION
            SELECT 
                'Rejected' as stage,
                COUNT(*) as count
            FROM candidate_positions
            WHERE status = 'Rejected'
        `;
        db.query(query, callback);
    },

    getMonthlyHiringStats: (year, callback) => {
        const query = `
            SELECT 
                MONTH(created_at) as month,
                COUNT(*) as applications,
                SUM(CASE WHEN status = 'Selected' THEN 1 ELSE 0 END) as hired
            FROM candidates
            WHERE YEAR(created_at) = ?
            GROUP BY MONTH(created_at)
            ORDER BY month
        `;
        db.query(query, [year], callback);
    },

    getDepartmentCandidateStats: (callback) => {
        const query = `
            SELECT 
                d.name as department,
                COUNT(cp.candidate_id) as candidates
            FROM departments d
            LEFT JOIN positions p ON d.id = p.department_id
            LEFT JOIN candidate_positions cp ON p.id = cp.position_id
            GROUP BY d.id, d.name
            ORDER BY candidates DESC
        `;
        db.query(query, callback);
    },

// Fixed getCandidateById that returns the properly formatted data


    updateCandidate: (id, candidateData, callback) => {
        const query = 'UPDATE candidates SET ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [candidateData, id], callback);
    },

    deleteCandidate: (id, callback) => {
        const query = 'DELETE FROM candidates WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCandidates: (ids, callback) => {
        const query = 'DELETE FROM candidates WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // This should be in your interviewModel.js
// Check if it's returning the entire candidate data or just an empty array

getCandidateById: (id, callback) => {
    const query = `
        SELECT c.*,
               g.name AS gender_name,
               d.name AS department_name,
               ds.name AS designation_name,
               et.name AS employment_type_name,
               bg.name AS blood_group_name, 
               cu.username AS created_by_username,
               uu.username AS updated_by_username
        FROM candidates c
        LEFT JOIN genders g ON c.genderId = g.id
        LEFT JOIN departments d ON c.departmentId = d.id
        LEFT JOIN designations ds ON c.designationId = ds.id
        LEFT JOIN employment_types et ON c.employmentTypeId = et.id
        LEFT JOIN blood_groups bg ON c.bloodGroupId = bg.id
        LEFT JOIN users cu ON c.created_by = cu.id
        LEFT JOIN users uu ON c.updated_by = uu.id
        WHERE c.id = ?
    `;
    
    db.query(query, [id], (err, results) => {
        if (err) {
            console.error('Database error in getCandidateById:', err);
            callback(err, []);
            return;
        }
        
        console.log('Database query results:', results);
        
        if (results.length === 0) {
            console.log('No candidate found with ID:', id);
            callback(null, []);
            return;
        }
        
        // Return the candidate data directly
        callback(null, results);
    });
},

   // Fix the getCandidatePositions method in interviewModel.js
getCandidatePositions: (candidateId, callback) => {
    const query = `
        SELECT cp.*,
               p.title AS position_title,
               p.department_id,
               d.name AS department_name,
               p.vacancies,
               p.status AS position_status,
               cu.username AS created_by_username
        FROM candidate_positions cp
        LEFT JOIN positions p ON cp.position_id = p.id
        LEFT JOIN departments d ON p.department_id = d.id
        LEFT JOIN users cu ON cp.created_by = cu.id
        WHERE cp.candidate_id = ?
        ORDER BY cp.application_date DESC
    `;
    db.query(query, [candidateId], (err, results) => {
        if (err) {
            console.error('Error in getCandidatePositions:', err);
            callback(err, []);
            return;
        }
        
        // Transform field names and provide default values
        const transformedResults = results.map(position => ({
            id: position.id,
            candidate_id: position.candidate_id,
            position_id: position.position_id,
            application_date: position.application_date,
            status: position.status || 'Applied',
            position_title: position.position_title || '',
            department_id: position.department_id,
            department_name: position.department_name || '',
            vacancies: position.vacancies || 0,
            position_status: position.position_status || 'Open',
            created_by_username: position.created_by_username || ''
        }));
        
        callback(null, transformedResults);
    });
},

    getPositionCandidates: (positionId, callback) => {
        const query = `
            SELECT cp.*,
                   c.firstName,
                   c.lastName,
                   c.email,
                   c.phoneNumber,
                   c.totalExperience,
                   c.currentOrganization,
                   c.expectedSalary,
                   c.resumeFile,
                   c.profilePicture,
                   c.status AS candidate_status,
                   (
                       SELECT COUNT(*) 
                       FROM interview_sessions 
                       WHERE candidate_id = c.id AND position_id = cp.position_id
                   ) AS interview_count
            FROM candidate_positions cp
            LEFT JOIN candidates c ON cp.candidate_id = c.id
            WHERE cp.position_id = ?
            ORDER BY cp.application_date DESC
        `;
        db.query(query, [positionId], callback);
    },

    updateCandidatePositionStatus: (candidateId, positionId, status, callback) => {
        const query = `
            UPDATE candidate_positions 
            SET status = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE candidate_id = ? AND position_id = ?
        `;
        db.query(query, [status, candidateId, positionId], callback);
    },

    removeCandidateFromPosition: (candidateId, positionId, callback) => {
        const query = 'DELETE FROM candidate_positions WHERE candidate_id = ? AND position_id = ?';
        db.query(query, [candidateId, positionId], callback);
    }
};

// Ensure tables are created when the application starts
Interview.createPositionsTable();
Interview.createCandidatesTable();
Interview.createCandidatePositionsTable();
Interview.createCandidateSiblingsTable();
Interview.createInterviewSessionsTable();
Interview.createCandidateDecisionsTable();

module.exports = Interview;