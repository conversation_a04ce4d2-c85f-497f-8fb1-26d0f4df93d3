const db = require('../config/database');

const Role = {
    createTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating roles table:', err);
            } else {
                console.log('Roles table ensured');
            }
        });
    },

    create: ({ name, permissions }, callback) => {
        // Check for duplicate role name
        const checkQuery = 'SELECT * FROM roles WHERE name = ?';
        db.query(checkQuery, [name], (err, results) => {
            if (err) {
                return callback(err);
            }

            if (results.length > 0) {
                return callback('Role name already exists');
            }

            // Insert new role
            const query = 'INSERT INTO roles (name, permissions) VALUES (?, ?)';
            db.query(query, [name, JSON.stringify(permissions)], callback);
        });
    },

    findAll: (callback) => {
        const query = 'SELECT * FROM roles';
        db.query(query, (err, results) => {
            if (err) {
                return callback(err);
            }
            callback(null, results);
        });
    },

    findById: (id, callback) => {
        const query = 'SELECT * FROM roles WHERE id = ?';
        db.query(query, [id], (err, results) => {
            if (err || results.length === 0) {
                return callback(err || 'Role not found');
            }
            callback(null, results[0]);
        });
    },

    findByName: (name, callback) => {
        const query = 'SELECT * FROM roles WHERE name = ?';
        db.query(query, [name], (err, results) => {
            if (err) {
                return callback(err);
            }
            if (results.length === 0) {
                return callback(null, null); // No role found
            }
            callback(null, results[0]);
        });
    },

    update: (id, { name, permissions }, callback) => {
        const query = 'UPDATE roles SET name = ?, permissions = ? WHERE id = ?';
        db.query(query, [name, JSON.stringify(permissions), id], callback);
    },

    delete: (id, callback) => {
        const query = 'DELETE FROM roles WHERE id = ?';
        db.query(query, [id], callback);
    }
};

// Ensure the table is created when the application starts
Role.createTable();

module.exports = Role;
