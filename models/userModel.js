const db = require('../config/database');
const bcrypt = require('bcrypt');

const User = {
    createTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                role_id INT NOT NULL,
                mobile_number VARCHAR(15) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL UNIQUE,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id),
                FOREIGN KEY (role_id) REFERENCES roles(id)
            )
        `;
        db.query(query, (err, results) => {
            if (err) {
                console.error('Error creating users table:', err);
            } else {
                console.log('Users table ensured');
            }
        });
    },

    createUser: ({ username, password, role_id, mobile_number, email, is_active, created_by }, callback) => {
        const checkQuery = 'SELECT * FROM users WHERE username = ? OR email = ? OR mobile_number = ?';
        db.query(checkQuery, [username, email, mobile_number], (err, results) => {
            if (err) return callback(err);
            if (results.length > 0) {
                const field = results[0].username === username ? 'Username' :
                              results[0].email === email ? 'Email' : 'Mobile number';
                return callback(`${field} already exists`);
            }

            const query = 'INSERT INTO users (username, password, role_id, mobile_number, email, is_active, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)';
            db.query(query, [username, password, role_id, mobile_number, email, is_active, created_by], callback);
        });
    },




    getPasswordHash: (username, callback) => {
        const query = 'SELECT password FROM users WHERE username = ?';
        db.query(query, [username], (err, results) => {
            if (err) {
                return callback(err);
            }
            if (results.length === 0) {
                return callback('User not found');
            }
            callback(null, results[0].password);
        });
    },



    getAllUsers: (callback) => {
        const query = `
            SELECT u.*,
                   r.name AS role_name,
                   c.username AS created_by_name,
                   up.username AS updated_by_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            LEFT JOIN users c ON u.created_by = c.id
            LEFT JOIN users up ON u.updated_by = up.id
        `;
        db.query(query, (err, results) => {
            if (err) {
                return callback(err);
            }
            callback(null, results);
        });
    },

    getUserById: (id, callback) => {
        const query = `
            SELECT u.*,
                   r.name AS role_name,
                   c.username AS created_by_name,
                   up.username AS updated_by_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            LEFT JOIN users c ON u.created_by = c.id
            LEFT JOIN users up ON u.updated_by = up.id
            WHERE u.id = ?
        `;
        db.query(query, [id], (err, results) => {
            if (err) {
                return callback(err);
            }
            if (results.length === 0) {
                return callback('User not found');
            }
            callback(null, results[0]);
        });
    },

    updateUser: ({ id, username, role_id, mobile_number, email, is_active, updated_by }, callback) => {
        const query = 'UPDATE users SET username = ?, role_id = ?, mobile_number = ?, email = ?, is_active = ?, updated_by = ? WHERE id = ?';
        db.query(query, [username, role_id, mobile_number, email, is_active, updated_by, id], (err, result) => {
            if (err) {
                return callback(err);
            }
            if (result.affectedRows === 0) {
                return callback('User not found');
            }
            callback(null, { message: 'User updated successfully' });
        });
    },

    deleteUser: (id, callback) => {
        const query = 'DELETE FROM users WHERE id = ?';
        db.query(query, [id], (err, result) => {
            if (err) {
                return callback(err);
            }
            if (result.affectedRows === 0) {
                return callback('User not found');
            }
            callback(null, { message: 'User deleted successfully' });

        });
    },


    deleteUsers: (ids, callback) => {
        const query = 'DELETE FROM users WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    
    findUserByUsernameAndPassword: (username, password, callback) => {
        const query = 'SELECT * FROM users WHERE username = ? AND is_active = TRUE';
        db.query(query, [username], (err, results) => {
            if (err || results.length === 0) return callback(err || 'User not found or inactive');
            
            const user = results[0];
            bcrypt.compare(password, user.password, (err, isMatch) => {
                if (err || !isMatch) return callback('Invalid credentials');
                callback(null, user);
            });
        });
    },

    findUserByEmailAndPassword: (email, password, callback) => {
        const query = 'SELECT * FROM users WHERE email = ? AND is_active = TRUE';
        db.query(query, [email], (err, results) => {
            if (err || results.length === 0) {
                return callback(err || 'User not found or inactive');
            }

            const user = results[0];
            bcrypt.compare(password, user.password, (err, isMatch) => {
                if (err || !isMatch) {
                    return callback(err || 'Invalid credentials');
                }
                callback(null, user);
            });
        });
    },

    findUserByMobileNumber: (mobile_number, callback) => {
        const query = 'SELECT * FROM users WHERE mobile_number = ? AND is_active = TRUE';
        db.query(query, [mobile_number], (err, results) => {
            if (err || results.length === 0) {
                return callback(err || 'User not found or inactive');
            }
            callback(null, results[0]);
        });
    },

    findUserByMobileNumberAndPassword: (mobile_number, password, callback) => {
        const query = 'SELECT * FROM users WHERE mobile_number = ? AND is_active = TRUE';
        db.query(query, [mobile_number], (err, results) => {
            if (err || results.length === 0) {
                return callback(err || 'User not found or inactive');
            }

            const user = results[0];
            bcrypt.compare(password, user.password, (err, isMatch) => {
                if (err || !isMatch) {
                    return callback(err || 'Invalid credentials');
                }
                callback(null, user);
            });
        });
    }



    
};





// Ensure the table is created when the application starts
User.createTable();

module.exports = User;