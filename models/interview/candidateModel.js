// models/interview/candidateModel.js

const db = require('../../config/database');

const candidateModel = {
    // Table creation
    createCandidatesTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                firstName VARCHAR(255) NOT NULL,
                lastName VARCHAR(255) NOT NULL,
                dateOfBirth DATE,
                genderId INT,
                profilePicture VARCHAR(255),
                email VARCHAR(255) NOT NULL,
                phoneNumber VARCHAR(20) NOT NULL,
                alternatePhone VARCHAR(20),
                emergencyContactName VARCHAR(255),
                emergencyContactNumber VARCHAR(20),
                address TEXT,
                permanentAddress TEXT,
                city VARCHAR(255),
                state VARCHAR(255),
                postalCode VARCHAR(20),
                employeeId VARCHAR(50),
                hireDate DATE,
                departmentId INT,
                designationId INT,
                employmentTypeId INT,
                educationLevel VARCHAR(255),
                degrees VARCHAR(255),
                university VARCHAR(255),
                yearOfPassing INT,
                specialization VARCHAR(255),
                additionalCertifications TEXT,
                totalExperience DECIMAL(5,2),
                currentOrganization VARCHAR(255),
                currentDesignation VARCHAR(255),
                currentSalary DECIMAL(12,2),
                expectedSalary DECIMAL(12,2),
                noticePeriod INT,
                reasonForChange TEXT,
                skills TEXT,
                salary DECIMAL(10,2),
                bloodGroupId INT,
                religion VARCHAR(255),
                community VARCHAR(255),
                idProofType VARCHAR(50),
                idProofNumber VARCHAR(100),
                idProof VARCHAR(255),
                referenceContacts TEXT,
                resumeFile VARCHAR(255),
                portfolioLinks TEXT,
                status ENUM('New','In Process','Selected','Rejected') DEFAULT 'New',
                father_name VARCHAR(255),
                father_occupation VARCHAR(255),
                father_contact VARCHAR(20),
                father_status ENUM('Living','Deceased') DEFAULT 'Living',
                mother_name VARCHAR(255),
                mother_occupation VARCHAR(255),
                mother_contact VARCHAR(20),
                mother_status ENUM('Living','Deceased') DEFAULT 'Living',
                family_address TEXT,
                isActive BOOLEAN DEFAULT TRUE,
                employmentStatus ENUM('Active','On Leave','Terminated'),
                created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                UNIQUE KEY (email),
                FOREIGN KEY (genderId) REFERENCES genders(id),
                FOREIGN KEY (bloodGroupId) REFERENCES blood_groups(id),
                FOREIGN KEY (departmentId) REFERENCES departments(id),
                FOREIGN KEY (designationId) REFERENCES designations(id),
                FOREIGN KEY (employmentTypeId) REFERENCES employment_types(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidates table:', err);
            } else {
                console.log('Candidates table ensured');
            }
        });
    },

    createCandidatePositionsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidate_positions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                position_id INT NOT NULL,
                application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('Applied', 'In Process', 'Selected', 'Rejected') DEFAULT 'Applied',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIGN KEY (position_id) REFERENCES positions(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                UNIQUE KEY unique_candidate_position (candidate_id, position_id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidate_positions table:', err);
            } else {
                console.log('Candidate positions table ensured');
            }
        });
    },

    // Candidate CRUD operations
    createCandidate: (candidateData, callback) => {
        const query = 'INSERT INTO candidates SET ?';
        db.query(query, candidateData, callback);
    },

    getAllCandidates: (callback) => {
        const query = `
            SELECT c.*,
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username,
                   (
                       SELECT COUNT(*) 
                       FROM candidate_positions 
                       WHERE candidate_id = c.id
                   ) AS position_count,
                   (
                       SELECT COUNT(*) 
                       FROM interview_sessions 
                       WHERE candidate_id = c.id
                   ) AS interview_count
            FROM candidates c
            LEFT JOIN genders g ON c.genderId = g.id
            LEFT JOIN departments d ON c.departmentId = d.id
            LEFT JOIN designations ds ON c.designationId = ds.id
            LEFT JOIN employment_types et ON c.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON c.bloodGroupId = bg.id
            LEFT JOIN users cu ON c.created_by = cu.id
            LEFT JOIN users uu ON c.updated_by = uu.id
            ORDER BY c.created_at DESC
        `;
        db.query(query, callback);
    },

    getCandidateById: (id, callback) => {
        const query = `
            SELECT c.*,
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name, 
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username
            FROM candidates c
            LEFT JOIN genders g ON c.genderId = g.id
            LEFT JOIN departments d ON c.departmentId = d.id
            LEFT JOIN designations ds ON c.designationId = ds.id
            LEFT JOIN employment_types et ON c.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON c.bloodGroupId = bg.id
            LEFT JOIN users cu ON c.created_by = cu.id
            LEFT JOIN users uu ON c.updated_by = uu.id
            WHERE c.id = ?
        `;
        db.query(query, [id], callback);
    },

    updateCandidate: (id, candidateData, callback) => {
        const query = 'UPDATE candidates SET ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [candidateData, id], callback);
    },

    deleteCandidate: (id, callback) => {
        const query = 'DELETE FROM candidates WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCandidates: (ids, callback) => {
        const query = 'DELETE FROM candidates WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    updateCandidateStatus: (id, status, updated_by, callback) => {
        const query = `
            UPDATE candidates 
            SET status = ?,
                updated_by = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        db.query(query, [status, updated_by, id], callback);
    },

    // Candidate Position Mapping operations
    assignCandidateToPosition: (mappingData, callback) => {
        const query = 'INSERT INTO candidate_positions SET ?';
        db.query(query, mappingData, callback);
    },

    getCandidatePositions: (candidateId, callback) => {
        const query = `
            SELECT cp.*,
                   p.title AS position_title,
                   p.department_id,
                   d.name AS department_name,
                   p.vacancies,
                   p.status AS position_status,
                   cu.username AS created_by_username
            FROM candidate_positions cp
            LEFT JOIN positions p ON cp.position_id = p.id
            LEFT JOIN departments d ON p.department_id = d.id
            LEFT JOIN users cu ON cp.created_by = cu.id
            WHERE cp.candidate_id = ?
            ORDER BY cp.application_date DESC
        `;
        db.query(query, [candidateId], callback);
    },

    getPositionCandidates: (positionId, callback) => {
        const query = `
            SELECT cp.*,
                   c.firstName,
                   c.lastName,
                   c.email,
                   c.phoneNumber,
                   c.totalExperience,
                   c.currentOrganization,
                   c.expectedSalary,
                   c.resumeFile,
                   c.profilePicture,
                   c.status AS candidate_status,
                   (
                       SELECT COUNT(*) 
                       FROM interview_sessions 
                       WHERE candidate_id = c.id AND position_id = cp.position_id
                   ) AS interview_count
            FROM candidate_positions cp
            LEFT JOIN candidates c ON cp.candidate_id = c.id
            WHERE cp.position_id = ?
            ORDER BY cp.application_date DESC
        `;
        db.query(query, [positionId], callback);
    },

    updateCandidatePositionStatus: (candidateId, positionId, status, callback) => {
        const query = `
            UPDATE candidate_positions 
            SET status = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE candidate_id = ? AND position_id = ?
        `;
        db.query(query, [status, candidateId, positionId], callback);
    },

    removeCandidateFromPosition: (candidateId, positionId, callback) => {
        const query = 'DELETE FROM candidate_positions WHERE candidate_id = ? AND position_id = ?';
        db.query(query, [candidateId, positionId], callback);
    },

    // Search and reporting
    searchCandidates: (searchTerms, callback) => {
        let query = `
            SELECT c.*,
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name,
                   (
                       SELECT COUNT(*) 
                       FROM candidate_positions 
                       WHERE candidate_id = c.id
                   ) AS position_count,
                   (
                       SELECT COUNT(*) 
                       FROM interview_sessions 
                       WHERE candidate_id = c.id
                   ) AS interview_count
            FROM candidates c
            LEFT JOIN genders g ON c.genderId = g.id
            LEFT JOIN departments d ON c.departmentId = d.id
            LEFT JOIN designations ds ON c.designationId = ds.id
            LEFT JOIN employment_types et ON c.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON c.bloodGroupId = bg.id
            WHERE 1=1
        `;
        
        const queryParams = [];
        
        // Add search conditions
        if (searchTerms.name) {
            query += ` AND (c.firstName LIKE ? OR c.lastName LIKE ? OR CONCAT(c.firstName, ' ', c.lastName) LIKE ?)`;
            const searchPattern = `%${searchTerms.name}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern);
        }
        
        if (searchTerms.email) {
            query += ` AND c.email LIKE ?`;
            queryParams.push(`%${searchTerms.email}%`);
        }
        
        if (searchTerms.phoneNumber) {
            query += ` AND c.phoneNumber LIKE ?`;
            queryParams.push(`%${searchTerms.phoneNumber}%`);
        }
        
        if (searchTerms.status) {
            query += ` AND c.status = ?`;
            queryParams.push(searchTerms.status);
        }
        
        if (searchTerms.min_experience) {
            query += ` AND c.totalExperience >= ?`;
            queryParams.push(searchTerms.min_experience);
        }
        
        if (searchTerms.skills) {
            query += ` AND c.skills LIKE ?`;
            queryParams.push(`%${searchTerms.skills}%`);
        }
        
        if (searchTerms.position_id) {
            query += ` AND c.id IN (SELECT candidate_id FROM candidate_positions WHERE position_id = ?)`;
            queryParams.push(searchTerms.position_id);
        }
        
        query += ` ORDER BY c.created_at DESC`;
        
        db.query(query, queryParams, callback);
    },

    // Dashboard methods
    getRecentCandidates: (limit, callback) => {
        const query = `
            SELECT c.id, c.firstName, c.lastName, c.email, c.status, c.profilePicture,
                   cp.position_id,
                   p.title AS position_title
            FROM candidates c
            LEFT JOIN candidate_positions cp ON c.id = cp.candidate_id
            LEFT JOIN positions p ON cp.position_id = p.id
            ORDER BY c.created_at DESC
            LIMIT ?
        `;
        
        db.query(query, [limit || 5], callback);
    }
};

module.exports = candidateModel;