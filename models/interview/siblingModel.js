// models/interview/siblingModel.js

const db = require('../../config/database');

const siblingModel = {
    // Table creation
    createCandidateSiblingsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS candidate_siblings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                candidate_id INT NOT NULL,
                name VA<PERSON>HA<PERSON>(255) NOT NULL,
                date_of_birth DATE NOT NULL,
                gender VARCHAR(50),
                occupation VARCHAR(255),
                marital_status VARCHAR(50),
                contact VARCHAR(20),
                status ENUM('Living','Deceased') DEFAULT 'Living',
                is_emergency_contact BOOLEAN DEFAULT FALSE,
                additional_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                <PERSON>OREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
                FOREIG<PERSON> KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating candidate_siblings table:', err);
            } else {
                console.log('Candidate siblings table ensured');
            }
        });
    },

    // CRUD operations for siblings
    createCandidateSibling: (siblingData, callback) => {
        const query = 'INSERT INTO candidate_siblings SET ?';
        db.query(query, siblingData, callback);
    },

    getCandidateSiblings: (candidateId, callback) => {
        const query = `
            SELECT s.*, 
                   TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age,
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM candidate_siblings s
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.candidate_id = ?
            ORDER BY s.date_of_birth
        `;
        db.query(query, [candidateId], callback);
    },

    getSiblingById: (id, callback) => {
        const query = `
            SELECT s.*, 
                   TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age,
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM candidate_siblings s
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.id = ?
        `;
        db.query(query, [id], callback);
    },

    updateCandidateSibling: (id, siblingData, callback) => {
        const query = `
            UPDATE candidate_siblings 
            SET 
                name = ?,
                date_of_birth = ?,
                gender = ?,
                occupation = ?,
                marital_status = ?,
                contact = ?,
                status = ?,
                is_emergency_contact = ?,
                additional_info = ?,
                updated_by = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        
        const values = [
            siblingData.name,
            siblingData.date_of_birth,
            siblingData.gender,
            siblingData.occupation,
            siblingData.marital_status,
            siblingData.contact,
            siblingData.status,
            siblingData.is_emergency_contact,
            siblingData.additional_info,
            siblingData.updated_by,
            id
        ];
    
        db.query(query, values, callback);
    },

    deleteCandidateSibling: (id, callback) => {
        const query = 'DELETE FROM candidate_siblings WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCandidateSiblings: (candidateId, callback) => {
        const query = 'DELETE FROM candidate_siblings WHERE candidate_id = ?';
        db.query(query, [candidateId], callback);
    }
};

module.exports = siblingModel;