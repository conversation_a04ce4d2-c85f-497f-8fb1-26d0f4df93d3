// models/interview/publicMasterModel.js
const db = require('../../config/database');

const PublicMasterModel = {
  /**
   * Get all genders
   * @param {function} callback - Callback function with (error, results)
   */
  getAllGenders: (callback) => {
    try {
      console.log('Executing getAllGenders query');
      // Remove is_active filter
      const query = 'SELECT id, name FROM genders';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllGenders:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} genders`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllGenders method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all blood groups
   * @param {function} callback - Callback function with (error, results)
   */
  getAllBloodGroups: (callback) => {
    try {
      console.log('Executing getAllBloodGroups query');
      // Remove is_active filter
      const query = 'SELECT id, name FROM blood_groups';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllBloodGroups:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} blood groups`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllBloodGroups method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all religions
   * @param {function} callback - Callback function with (error, results)
   */
  getAllReligions: (callback) => {
    try {
      console.log('Executing getAllReligions query');
      // Remove is_active filter
      const query = 'SELECT id, name FROM religions';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllReligions:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} religions`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllReligions method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all communities
   * @param {function} callback - Callback function with (error, results)
   */
  getAllCommunities: (callback) => {
    try {
      console.log('Executing getAllCommunities query');
      // Remove is_active filter
      const query = 'SELECT id, name FROM communities';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllCommunities:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} communities`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllCommunities method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all states
   * @param {function} callback - Callback function with (error, results)
   */
  getAllStates: (callback) => {
    try {
      console.log('Executing getAllStates query');
      // Remove is_active filter
      const query = 'SELECT id, name FROM states';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllStates:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} states`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllStates method:', e);
      callback(e, null);
    }
  },

  /**
   * Get cities for a specific state
   * @param {number} stateId - ID of the state
   * @param {function} callback - Callback function with (error, results)
   */
  getCitiesByStateId: (stateId, callback) => {
    try {
      console.log(`Executing getCitiesByStateId query for state ID: ${stateId}`);
      // Remove is_active filter
      const query = 'SELECT id, name FROM cities WHERE state_id = ?';
      db.query(query, [stateId], (err, results) => {
        if (err) {
          console.error('Database error in getCitiesByStateId:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} cities for state ID ${stateId}`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getCitiesByStateId method:', e);
      callback(e, null);
    }
  },

  /**
   * Get open positions for public view
   * @param {function} callback - Callback function with (error, results)
   */
  getOpenPositions: (callback) => {
    try {
      console.log('Executing getOpenPositions query');
      const query = `
        SELECT p.id, p.title, p.required_skills, p.required_qualifications,
               p.min_experience, p.max_experience, p.vacancies,
               d.name as department_name
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE p.status = 'Open'
      `;
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getOpenPositions:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} open positions`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getOpenPositions method:', e);
      callback(e, null);
    }
  },

  /**
   * Get position details by ID for public view
   * @param {number} positionId - ID of the position
   * @param {function} callback - Callback function with (error, results)
   */
  getPublicPositionById: (positionId, callback) => {
    try {
      console.log(`Executing getPublicPositionById query for position ID: ${positionId}`);
      const query = `
        SELECT p.id, p.title, p.required_skills, p.required_qualifications,
               p.min_experience, p.max_experience, p.vacancies,
               d.name as department_name
        FROM positions p
        LEFT JOIN departments d ON p.department_id = d.id
        WHERE p.id = ? AND p.status = 'Open'
      `;
      db.query(query, [positionId], (err, results) => {
        if (err) {
          console.error('Database error in getPublicPositionById:', err);
        } else {
          console.log(`Retrieved position info. Found: ${results?.length > 0}`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getPublicPositionById method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all departments for public view
   * @param {function} callback - Callback function with (error, results)
   */
  getAllDepartments: (callback) => {
    try {
      console.log('Executing getAllDepartments query');
      // Use is_active for departments since it does have this column
      const query = 'SELECT id, name FROM departments WHERE is_active = 1';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllDepartments:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} departments`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllDepartments method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all designations for public view
   * @param {function} callback - Callback function with (error, results)
   */
  getAllDesignations: (callback) => {
    try {
      console.log('Executing getAllDesignations query');
      // Use is_active for designations since it does have this column
      const query = 'SELECT id, name FROM designations WHERE is_active = 1';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllDesignations:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} designations`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllDesignations method:', e);
      callback(e, null);
    }
  },

  /**
   * Get all employment types for public view
   * @param {function} callback - Callback function with (error, results)
   */
  getAllEmploymentTypes: (callback) => {
    try {
      console.log('Executing getAllEmploymentTypes query');
      // Use is_active for employment_types since it does have this column
      const query = 'SELECT id, name FROM employment_types WHERE is_active = 1';
      db.query(query, (err, results) => {
        if (err) {
          console.error('Database error in getAllEmploymentTypes:', err);
        } else {
          console.log(`Retrieved ${results?.length || 0} employment types`);
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in getAllEmploymentTypes method:', e);
      callback(e, null);
    }
  },

  /**
   * Basic database connection test
   * @param {function} callback - Callback function with (error, results)
   */
  testDatabaseConnection: (callback) => {
    try {
      console.log('Testing database connection');
      db.query('SELECT 1 AS test', (err, results) => {
        if (err) {
          console.error('Database connection test failed:', err);
        } else {
          console.log('Database connection test successful');
        }
        callback(err, results);
      });
    } catch (e) {
      console.error('Error in testDatabaseConnection method:', e);
      callback(e, null);
    }
  }
};

module.exports = PublicMasterModel;