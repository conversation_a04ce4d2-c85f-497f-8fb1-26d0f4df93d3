// models/interview/candidateCommentModel.js

const db = require('../../config/database');

const CandidateComment = {
  // Add a new comment
  createComment: (commentData, callback) => {
    const query = `
      INSERT INTO candidate_comments 
      (candidate_id, field_identifier, comment_text, user_id, is_read, status) 
      VALUES (?, ?, ?, ?, false, 'active')
    `;
    
    db.query(
      query, 
      [commentData.candidate_id, commentData.field_identifier, commentData.comment_text, commentData.user_id], 
      callback
    );
  },
  
  // Check if comment exists for a field
  checkCommentExists: (candidateId, fieldIdentifier, callback) => {
    const query = `
      SELECT id FROM candidate_comments 
      WHERE candidate_id = ? AND field_identifier = ? AND status = 'active'
    `;
    
    db.query(query, [candidateId, fieldIdentifier], callback);
  },
  
  // Get comment by ID
  getCommentById: (commentId, callback) => {
    const query = `SELECT * FROM candidate_comments WHERE id = ?`;
    db.query(query, [commentId], callback);
  },
  
  // Update an existing comment
  updateComment: (commentId, commentText, callback) => {
    const query = `
      UPDATE candidate_comments 
      SET comment_text = ?, updated_at = CURRENT_TIMESTAMP, is_read = false 
      WHERE id = ?
    `;
    
    db.query(query, [commentText, commentId], callback);
  },
  
  // Get all comments for a candidate
  getCandidateComments: (candidateId, callback) => {
    const query = `
      SELECT cc.*, u.username 
      FROM candidate_comments cc
      JOIN users u ON cc.user_id = u.id
      WHERE cc.candidate_id = ? AND cc.status = 'active'
      ORDER BY cc.field_identifier, cc.created_at DESC
    `;
    
    db.query(query, [candidateId], callback);
  },
  
  // Get comments for a specific field
  getFieldComments: (candidateId, fieldIdentifier, callback) => {
    const query = `
      SELECT cc.*, u.username 
      FROM candidate_comments cc
      JOIN users u ON cc.user_id = u.id
      WHERE cc.candidate_id = ? AND cc.field_identifier = ? AND cc.status = 'active'
      ORDER BY cc.created_at DESC
    `;
    
    db.query(query, [candidateId, fieldIdentifier], callback);
  },
  
  // Mark a comment as read
  markAsRead: (commentId, callback) => {
    const query = `
      UPDATE candidate_comments 
      SET is_read = true, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    db.query(query, [commentId], callback);
  },
  
  // Delete a comment (soft delete)
  deleteComment: (commentId, callback) => {
    const query = `
      UPDATE candidate_comments 
      SET status = 'deleted', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    db.query(query, [commentId], callback);
  }
};

module.exports = CandidateComment;