const db = require('../config/database');

const Staff = {
    createStaffTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS staff (
                id INT AUTO_INCREMENT PRIMARY KEY,
                firstName VARCHAR(255) NOT NULL,
                lastName VARCHAR(255) NOT NULL,
                dateOfBirth DATE NOT NULL,
                genderId INT NOT NULL,
                profilePicture VARCHAR(255),
                email VARCHAR(255) NOT NULL UNIQUE,
                phoneNumber VARCHAR(20) NOT NULL,
                emergencyContactName VARCHAR(255) NOT NULL,
                emergencyContactNumber VARCHAR(20) NOT NULL,
                address TEXT NOT NULL,
                city VARCHAR(255) NOT NULL,
                state VARCHAR(255) NOT NULL,
                postalCode VARCHAR(20) NOT NULL,
                employeeId VARCHAR(50) UNIQUE,
                hireDate DATE NOT NULL,
                departmentId INT NOT NULL,
                designationId INT NOT NULL,
                employmentTypeId INT NOT NULL,
                educationLevel VARCHAR(255) NOT NULL,
                degrees VARCHAR(255),
                salary DECIMAL(10, 2) NOT NULL,
                bloodGroupId INT NOT NULL,
                religion VARCHAR(255),
                community VARCHAR(255),
                idProof VARCHAR(255) NOT NULL,
                
                father_name VARCHAR(255),
                father_occupation VARCHAR(255),
                father_contact VARCHAR(20),
                father_status ENUM('Living', 'Deceased') DEFAULT 'Living',
                mother_name VARCHAR(255),
                mother_occupation VARCHAR(255),
                mother_contact VARCHAR(20),
                mother_status ENUM('Living', 'Deceased') DEFAULT 'Living',
                family_address TEXT,
                
                isActive BOOLEAN DEFAULT TRUE,
                employmentStatus ENUM('Active', 'On Leave', 'Terminated') DEFAULT 'Active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (genderId) REFERENCES genders(id),
                FOREIGN KEY (departmentId) REFERENCES departments(id),
                FOREIGN KEY (designationId) REFERENCES designations(id),
                FOREIGN KEY (employmentTypeId) REFERENCES employment_types(id),
                FOREIGN KEY (bloodGroupId) REFERENCES blood_groups(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating staff table:', err);
            } else {
                console.log('Staff table ensured');
            }
        });
    },

    createSiblingsTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS siblings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                staff_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                date_of_birth DATE NOT NULL,
                gender VARCHAR(50),
                occupation VARCHAR(255),
                marital_status VARCHAR(50),
                contact VARCHAR(20),
                status ENUM('Living', 'Deceased') DEFAULT 'Living',
                is_emergency_contact BOOLEAN DEFAULT FALSE,
                additional_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (staff_id) REFERENCES staff(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating siblings table:', err);
            } else {
                console.log('Siblings table ensured');
            }
        });
    },

    createStaff: (staffData, callback) => {
        try {
            // Clean the data by removing undefined/null values
            const cleanedData = {};
            for (let [key, value] of Object.entries(staffData)) {
                if (value !== undefined && value !== null) {
                    cleanedData[key] = value;
                }
            }
    
            // Log the cleaned data for debugging
            console.log('Cleaned staff data:', cleanedData);
    
            const query = 'INSERT INTO staff SET ?';
            db.query(query, cleanedData, (error, results) => {
                if (error) {
                    console.error('Database error in createStaff:', error);
                    callback(error);
                } else {
                    callback(null, results);
                }
            });
        } catch (error) {
            console.error('Error in createStaff model:', error);
            callback(error);
        }
    },

    getAllStaff: (callback) => {
        const query = `
            SELECT s.*, 
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username,
                   (
                       SELECT COUNT(*)
                       FROM siblings
                       WHERE staff_id = s.id
                   ) as sibling_count
            FROM staff s
            LEFT JOIN genders g ON s.genderId = g.id
            LEFT JOIN departments d ON s.departmentId = d.id
            LEFT JOIN designations ds ON s.designationId = ds.id
            LEFT JOIN employment_types et ON s.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON s.bloodGroupId = bg.id
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            ORDER BY s.created_at DESC
        `;
        db.query(query, callback);
    },

    getStaffById: (id, callback) => {
        const query = `
            SELECT s.*, 
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name,
                   bg.name AS blood_group_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username
            FROM staff s
            LEFT JOIN genders g ON s.genderId = g.id
            LEFT JOIN departments d ON s.departmentId = d.id
            LEFT JOIN designations ds ON s.designationId = ds.id
            LEFT JOIN employment_types et ON s.employmentTypeId = et.id
            LEFT JOIN blood_groups bg ON s.bloodGroupId = bg.id
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.id = ?
        `;
        db.query(query, [id], callback);
    },

// In staffModel.js
updateStaff: (id, staffData, callback) => {
    try {
        // Remove siblings from staffData as it should be handled separately
        const { siblings, ...staffFields } = staffData;

        // Clean the data by removing undefined/null values
        const cleanedData = {};
        for (let [key, value] of Object.entries(staffFields)) {
            if (value !== undefined && value !== null) {
                cleanedData[key] = value;
            }
        }

        const query = 'UPDATE staff SET ? WHERE id = ?';
        db.query(query, [cleanedData, id], callback);
    } catch (error) {
        console.error('Error in updateStaff model:', error);
        callback(error);
    }
},

    deleteStaff: (id, callback) => {
        // Note: Siblings will be automatically deleted due to ON DELETE CASCADE
        const query = 'DELETE FROM staff WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteStaffs: (ids, callback) => {
        const query = 'DELETE FROM staff WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    toggleStaffStatus: (id, updated_by, callback) => {
        const query = `
            UPDATE staff 
            SET isActive = NOT isActive, 
                updated_by = ?, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        `;
        db.query(query, [updated_by, id], callback);
    },

    updateProfilePicture: (staffId, picturePath, updatedBy, callback) => {
        const query = `
            UPDATE staff 
            SET profilePicture = ?, 
                updated_by = ?, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        `;
        db.query(query, [picturePath, updatedBy, staffId], callback);
    },

    // Sibling management methods
    createSibling: (siblingData, callback) => {
        const query = 'INSERT INTO siblings SET ?';
        db.query(query, siblingData, callback);
    },

    getAllSiblings: (staffId, callback) => {
        const query = `
            SELECT s.*, 
                   TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age,
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM siblings s
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.staff_id = ?
            ORDER BY s.date_of_birth
        `;
        db.query(query, [staffId], callback);
    },

    getSiblingById: (id, callback) => {
        const query = `
            SELECT s.*, 
                   TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age,
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM siblings s
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.id = ?
        `;
        db.query(query, [id], callback);
    },

    updateSibling: (id, siblingData, callback) => {
        const query = `
            UPDATE siblings 
            SET 
                name = ?,
                date_of_birth = ?,
                gender = ?,
                occupation = ?,
                marital_status = ?,
                contact = ?,
                status = ?,
                is_emergency_contact = ?,
                updated_by = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        
        const values = [
            siblingData.name,
            siblingData.date_of_birth,
            siblingData.gender,
            siblingData.occupation,
            siblingData.marital_status,
            siblingData.contact,
            siblingData.status,
            siblingData.is_emergency_contact,
            siblingData.updated_by,
            id
        ];
    
        db.query(query, values, callback);
    },
    deleteSibling: (id, callback) => {
        const query = 'DELETE FROM siblings WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteSiblings: (ids, callback) => {
        const query = 'DELETE FROM siblings WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // Advanced search methods
    searchStaff: (searchParams, callback) => {
        let query = `
            SELECT s.*, 
                   g.name AS gender_name,
                   d.name AS department_name,
                   ds.name AS designation_name,
                   et.name AS employment_type_name
            FROM staff s
            LEFT JOIN genders g ON s.genderId = g.id
            LEFT JOIN departments d ON s.departmentId = d.id
            LEFT JOIN designations ds ON s.designationId = ds.id
            LEFT JOIN employment_types et ON s.employmentTypeId = et.id
            WHERE 1=1
        `;

        const queryParams = [];

        if (searchParams.name) {
            query += ` AND (s.firstName LIKE ? OR s.lastName LIKE ?)`;
            queryParams.push(`%${searchParams.name}%`, `%${searchParams.name}%`);
        }

        if (searchParams.departmentId) {
            query += ` AND s.departmentId = ?`;
            queryParams.push(searchParams.departmentId);
        }

        if (searchParams.designationId) {
            query += ` AND s.designationId = ?`;
            queryParams.push(searchParams.designationId);
        }

        if (searchParams.employmentStatus) {
            query += ` AND s.employmentStatus = ?`;
            queryParams.push(searchParams.employmentStatus);
        }

        query += ` ORDER BY s.created_at DESC`;

        db.query(query, queryParams, callback);
    },

    // Statistics and reporting methods
    getStaffStats: (callback) => {
        const query = `
            SELECT 
                COUNT(*) as total_staff,
                SUM(CASE WHEN isActive = 1 THEN 1 ELSE 0 END) as active_staff,
                SUM(CASE WHEN employmentStatus = 'Active' THEN 1 ELSE 0 END) as working_staff,
                SUM(CASE WHEN employmentStatus = 'On Leave' THEN 1 ELSE 0 END) as on_leave_staff,
                COUNT(DISTINCT departmentId) as total_departments
            FROM staff
        `;
        db.query(query, callback);
    },

    getDepartmentWiseCount: (callback) => {
        const query = `
            SELECT 
                d.name as department_name,
                COUNT(*) as staff_count
            FROM staff s
            JOIN departments d ON s.departmentId = d.id
            WHERE s.isActive = 1
            GROUP BY d.id, d.name
        `;
        db.query(query, callback);
    }
};

// Ensure tables are created when the application starts
Staff.createStaffTable();
Staff.createSiblingsTable();

module.exports = Staff;