const db = require('../config/database');
const { getUnassignedStaff } = require('../controllers/masterController');

const Master = {
    createStateTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS states (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating states table:', err);
            } else {
                console.log('States table ensured');
            }
        });
    },

    createCityTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS cities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                state_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                FOREIGN KEY (state_id) REFERENCES states(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating cities table:', err);
            } else {
                console.log('Cities table ensured');
            }
        });
    },

    createStoreTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS stores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                address VARCHAR(255) NOT NULL,
                city_id INT NOT NULL,
                state_id INT NOT NULL,
                postal_code VARCHAR(20),
                phone_number VARCHAR(20),
                email VARCHAR(255),
                opening_hours TIME,
                closing_hours TIME,
                manager_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (city_id) REFERENCES cities(id),
                FOREIGN KEY (state_id) REFERENCES states(id),
                FOREIGN KEY (manager_id) REFERENCES users(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating stores table:', err);
            } else {
                console.log('Stores table ensured');
            }
        });
    },

    createState: (name, created_by, callback) => {
        const query = 'INSERT INTO states (name, created_by) VALUES (?, ?)';
        db.query(query, [name, created_by], callback);
    },

    // getAllStates: (callback) => {
    //     const query = 'SELECT * FROM states';
    //     db.query(query, callback);
    // },

        getAllStates: (callback) => {
            const query = `
                SELECT s.*, u.username as created_by_username 
                FROM states s
                LEFT JOIN users u ON s.created_by = u.id
            `;
            db.query(query, callback);
        },

    getStateById: (id, callback) => {
        const query = 'SELECT * FROM states WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateState: (id, name, callback) => {
        const query = 'UPDATE states SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, id], callback);
    },

    deleteState: (id, callback) => {
        const query = 'DELETE FROM states WHERE id = ?';
        db.query(query, [id], callback);
    },

    createCity: (state_id, name, created_by, callback) => {
        const query = 'INSERT INTO cities (state_id, name, created_by) VALUES (?, ?, ?)';
        db.query(query, [state_id, name, created_by], callback);
    },

    getAllCities: (callback) => {
        const query = `
            SELECT c.*, s.name AS state_name, 
                   uc.username AS created_by_username, 
                   uu.username AS updated_by_username
            FROM cities c
            LEFT JOIN states s ON c.state_id = s.id
            LEFT JOIN users uc ON c.created_by = uc.id
            LEFT JOIN users uu ON c.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getCityById: (id, callback) => {
        const query = `
            SELECT c.*, s.name AS state_name, 
                   uc.username AS created_by_username, 
                   uu.username AS updated_by_username
            FROM cities c
            LEFT JOIN states s ON c.state_id = s.id
            LEFT JOIN users uc ON c.created_by = uc.id
            LEFT JOIN users uu ON c.updated_by = uu.id
            WHERE c.id = ?
        `;
        db.query(query, [id], callback);
    },

    getCitiesByStateId: (stateId, callback) => {
        const query = `
          SELECT c.*, s.name AS state_name
          FROM cities c
          LEFT JOIN states s ON c.state_id = s.id
          WHERE c.state_id = ?
        `;
        db.query(query, [stateId], callback);
      },

    updateCity: (id, state_id, name, updated_by, callback) => {
        const query = 'UPDATE cities SET state_id = ?, name = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [state_id, name, updated_by, id], callback);
    },

    deleteCity: (id, callback) => {
        const query = 'DELETE FROM cities WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCities: (ids, callback) => {
        const query = 'DELETE FROM cities WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    deleteStates: (ids, callback) => {
        const query = 'DELETE FROM states WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    createStore: (storeData, callback) => {
        const query = `
            INSERT INTO stores (
                name,
                address,
                city_id,
                state_id,
                postal_code,
                phone_number,
                email,
                opening_hours,
                closing_hours,
                manager_id,
                is_active,
                created_by,
                updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
    
        const values = [
            storeData.name,
            storeData.address,
            storeData.city_id,
            storeData.state_id,
            storeData.postal_code,
            storeData.phone_number,
            storeData.email,
            storeData.opening_hours,
            storeData.closing_hours,
            storeData.manager_id,
            storeData.is_active,
            storeData.created_by,
            storeData.created_by // Setting updated_by same as created_by initially
        ];
    
        // Log the query and values
        console.log('SQL Query:', query);
        console.log('Values:', values);
    
        db.query(query, values, callback);
    },

    getAllStores: (callback) => {
        const query = 'SELECT * FROM stores';
        db.query(query, callback);
    },

    getStoreById: (id, callback) => {
        const query = 'SELECT * FROM stores WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateStore: (id, storeData, callback) => {
        const query = `
            UPDATE stores SET 
            name = ?, address = ?, city_id = ?, state_id = ?, postal_code = ?, phone_number = ?, email = ?, opening_hours = ?, closing_hours = ?, manager_id = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        const { name, address, city_id, state_id, postal_code, phone_number, email, opening_hours, closing_hours, manager_id, updated_by } = storeData;
        db.query(query, [name, address, city_id, state_id, postal_code, phone_number, email, opening_hours, closing_hours, manager_id, updated_by, id], callback);
    },

    deleteStore: (id, callback) => {
        const query = 'DELETE FROM stores WHERE id = ?';
        db.query(query, [id], callback);
    },

    createBloodGroupTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS blood_groups (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(10) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating blood_groups table:', err);
            } else {
                console.log('Blood groups table ensured');
            }
        });
    },

    createBloodGroup: (name, created_by, callback) => {
        const query = 'INSERT INTO blood_groups (name, created_by, updated_by) VALUES (?, ?, ?)';
        db.query(query, [name, created_by, created_by], callback);
    },

    getAllBloodGroups: (callback) => {
        const query = `
            SELECT bg.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM blood_groups bg
            LEFT JOIN users cu ON bg.created_by = cu.id
            LEFT JOIN users uu ON bg.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getBloodGroupById: (id, callback) => {
        const query = 'SELECT * FROM blood_groups WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateBloodGroup: (id, name, updated_by, callback) => {
        const query = 'UPDATE blood_groups SET name = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, updated_by, id], callback);
    },

    deleteBloodGroup: (id, callback) => {
        const query = 'DELETE FROM blood_groups WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteBloodGroups: (ids, callback) => {
        const query = 'DELETE FROM blood_groups WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // Religion
    createReligionTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS religions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating religions table:', err);
            } else {
                console.log('Religions table ensured');
            }
        });
    },

    createReligion: (name, created_by, callback) => {
        const query = 'INSERT INTO religions (name, created_by, updated_by) VALUES (?, ?, ?)';
        db.query(query, [name, created_by, created_by], callback);
    },

    getAllReligions: (callback) => {
        const query = `
            SELECT r.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM religions r
            LEFT JOIN users cu ON r.created_by = cu.id
            LEFT JOIN users uu ON r.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getReligionById: (id, callback) => {
        const query = 'SELECT * FROM religions WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateReligion: (id, name, updated_by, callback) => {
        const query = 'UPDATE religions SET name = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, updated_by, id], callback);
    },

    deleteReligion: (id, callback) => {
        const query = 'DELETE FROM religions WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteReligions: (ids, callback) => {
        const query = 'DELETE FROM religions WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // Community
    createCommunityTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS communities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating communities table:', err);
            } else {
                console.log('Communities table ensured');
            }
        });
    },

    createCommunity: (name, created_by, callback) => {
        const query = 'INSERT INTO communities (name, created_by, updated_by) VALUES (?, ?, ?)';
        db.query(query, [name, created_by, created_by], callback);
    },

    getAllCommunities: (callback) => {
        const query = `
            SELECT c.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM communities c
            LEFT JOIN users cu ON c.created_by = cu.id
            LEFT JOIN users uu ON c.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getCommunityById: (id, callback) => {
        const query = 'SELECT * FROM communities WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateCommunity: (id, name, updated_by, callback) => {
        const query = 'UPDATE communities SET name = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, updated_by, id], callback);
    },

    deleteCommunity: (id, callback) => {
        const query = 'DELETE FROM communities WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteCommunities: (ids, callback) => {
        const query = 'DELETE FROM communities WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // Designation Table
    createDesignationTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS designations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating designations table:', err);
            } else {
                console.log('Designations table ensured');
            }
        });
    },

    createDesignation: (name, description, created_by, callback) => {
        const query = 'INSERT INTO designations (name, description, created_by, updated_by) VALUES (?, ?, ?, ?)';
        db.query(query, [name, description, created_by, created_by], callback);
    },

    getAllDesignations: (callback) => {
        const query = `
            SELECT d.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM designations d
            LEFT JOIN users cu ON d.created_by = cu.id
            LEFT JOIN users uu ON d.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getDesignationById: (id, callback) => {
        const query = 'SELECT * FROM designations WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateDesignation: (id, name, description, is_active, updated_by, callback) => {
        const query = 'UPDATE designations SET name = ?, description = ?, is_active = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, description, is_active, updated_by, id], callback);
    },

    deleteDesignation: (id, callback) => {
        const query = 'DELETE FROM designations WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteDesignations: (ids, callback) => {
        const query = 'DELETE FROM designations WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    // Department functions
    createDepartmentTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS departments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating departments table:', err);
            } else {
                console.log('Departments table ensured');
            }
        });
    },

    createDepartment: (name, is_active, created_by, callback) => {
        const query = 'INSERT INTO departments (name, is_active, created_by, updated_by) VALUES (?, ?, ?, ?)';
        db.query(query, [name, is_active, created_by, created_by], callback);
    },

    getAllDepartments: (callback) => {
        const query = `
            SELECT d.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM departments d
            LEFT JOIN users cu ON d.created_by = cu.id
            LEFT JOIN users uu ON d.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getDepartmentById: (id, callback) => {
        const query = 'SELECT * FROM departments WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateDepartment: (id, name, is_active, updated_by, callback) => {
        const query = 'UPDATE departments SET name = ?, is_active = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, is_active, updated_by, id], callback);
    },

    deleteDepartment: (id, callback) => {
        const query = 'DELETE FROM departments WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteDepartments: (ids, callback) => {
        const query = 'DELETE FROM departments WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    toggleDepartmentStatus: (id, updated_by, callback) => {
        const query = 'UPDATE departments SET is_active = NOT is_active, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [updated_by, id], callback);
    },

    // Employment Type functions
    createEmploymentTypeTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS employment_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating employment_types table:', err);
            } else {
                console.log('Employment types table ensured');
            }
        });
    },

    createEmploymentType: (name, description, created_by, callback) => {
        const query = 'INSERT INTO employment_types (name, description, created_by, updated_by) VALUES (?, ?, ?, ?)';
        db.query(query, [name, description, created_by, created_by], callback);
    },

    getAllEmploymentTypes: (callback) => {
        const query = `
            SELECT et.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM employment_types et
            LEFT JOIN users cu ON et.created_by = cu.id
            LEFT JOIN users uu ON et.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getEmploymentTypeById: (id, callback) => {
        const query = 'SELECT * FROM employment_types WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateEmploymentType: (id, name, description, is_active, updated_by, callback) => {
        const query = 'UPDATE employment_types SET name = ?, description = ?, is_active = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, description, is_active, updated_by, id], callback);
    },

    deleteEmploymentType: (id, callback) => {
        const query = 'DELETE FROM employment_types WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteEmploymentTypes: (ids, callback) => {
        const query = 'DELETE FROM employment_types WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    toggleEmploymentTypeStatus: (id, updated_by, callback) => {
        const query = 'UPDATE employment_types SET is_active = NOT is_active, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [updated_by, id], callback);
    },

    // Gender functions
    createGenderTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS genders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating genders table:', err);
            } else {
                console.log('Genders table ensured');
            }
        });
    },

    createGender: (name, is_active, created_by, callback) => {
        const query = 'INSERT INTO genders (name, is_active, created_by, updated_by) VALUES (?, ?, ?, ?)';
        db.query(query, [name, is_active, created_by, created_by], callback);
    },

    getAllGenders: (callback) => {
        const query = `
            SELECT g.*, 
                   cu.username as created_by_username,
                   uu.username as updated_by_username
            FROM genders g
            LEFT JOIN users cu ON g.created_by = cu.id
            LEFT JOIN users uu ON g.updated_by = uu.id
        `;
        db.query(query, callback);
    },

    getGenderById: (id, callback) => {
        const query = 'SELECT * FROM genders WHERE id = ?';
        db.query(query, [id], callback);
    },

    updateGender: (id, name, is_active, updated_by, callback) => {
        const query = 'UPDATE genders SET name = ?, is_active = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [name, is_active, updated_by, id], callback);
    },

    deleteGender: (id, callback) => {
        const query = 'DELETE FROM genders WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteGenders: (ids, callback) => {
        const query = 'DELETE FROM genders WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    toggleGenderStatus: (id, updated_by, callback) => {
        const query = 'UPDATE genders SET is_active = NOT is_active, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [updated_by, id], callback);
    },

    // Update the createStoreTable function to include is_active flag
    createStoreTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS stores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                address TEXT NOT NULL,
                city_id INT NOT NULL,
                state_id INT NOT NULL,
                postal_code VARCHAR(10),
                phone_number VARCHAR(15),
                email VARCHAR(255),
                opening_hours TIME,
                closing_hours TIME,
                manager_id INT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (city_id) REFERENCES cities(id),
                FOREIGN KEY (state_id) REFERENCES states(id),
                FOREIGN KEY (manager_id) REFERENCES users(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating stores table:', err);
            } else {
                console.log('Stores table ensured');
            }
        });
    },

    createStore: (storeData, callback) => {
        const query = `
            INSERT INTO stores (
                name,
                address,
                city_id,
                state_id,
                postal_code,
                phone_number,
                email,
                opening_hours,
                closing_hours,
                manager_id,
                is_active,
                created_by,
                updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            storeData.name,
            storeData.address,
            storeData.city_id,
            storeData.state_id,
            storeData.postal_code,
            storeData.phone_number,
            storeData.email,
            storeData.opening_hours,
            storeData.closing_hours,
            storeData.manager_id,
            storeData.is_active ? 1 : 0,  // Convert boolean to 1/0
            storeData.created_by,
            storeData.created_by  // Set updated_by same as created_by initially
        ];

        // Debug logging
        console.log('Store Data:', storeData);
        console.log('Query Values:', values);

        db.query(query, values, (error, results) => {
            if (error) {
                console.error('DB Error:', error);
                callback(error);
            } else {
                callback(null, results);
            }
        });
    },

    getAllStores: (callback) => {
        const query = `
            SELECT s.*, 
                   c.name AS city_name,
                   st.name AS state_name,
                   u.firstName AS manager_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username
            FROM stores s
            LEFT JOIN cities c ON s.city_id = c.id
            LEFT JOIN states st ON s.state_id = st.id
            LEFT JOIN staff u ON s.manager_id = u.id
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            ORDER BY s.created_at DESC
        `;
        db.query(query, callback);
    },

    getStoreById: (id, callback) => {
        const query = `
            SELECT s.*, 
                   c.name AS city_name,
                   st.name AS state_name,
                   u.username AS manager_name,
                   cu.username AS created_by_username,
                   uu.username AS updated_by_username
            FROM stores s
            LEFT JOIN cities c ON s.city_id = c.id
            LEFT JOIN states st ON s.state_id = st.id
            LEFT JOIN users u ON s.manager_id = u.id
            LEFT JOIN users cu ON s.created_by = cu.id
            LEFT JOIN users uu ON s.updated_by = uu.id
            WHERE s.id = ?
        `;
        db.query(query, [id], callback);
    },

    updateStore: (id, storeData, callback) => {
        const query = `
            UPDATE stores SET 
            name = ?,
            address = ?,
            city_id = ?,
            state_id = ?,
            postal_code = ?,
            phone_number = ?,
            email = ?,
            opening_hours = ?,
            closing_hours = ?,
            manager_id = ?,
            is_active = ?,
            updated_by = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        const values = [
            storeData.name,
            storeData.address,
            storeData.city_id,
            storeData.state_id,
            storeData.postal_code,
            storeData.phone_number,
            storeData.email,
            storeData.opening_hours,
            storeData.closing_hours,
            storeData.manager_id,
            storeData.is_active,
            storeData.updated_by,
            id
        ];
        db.query(query, values, callback);
    },

    deleteStore: (id, callback) => {
        const query = 'DELETE FROM stores WHERE id = ?';
        db.query(query, [id], callback);
    },

    deleteStores: (ids, callback) => {
        const query = 'DELETE FROM stores WHERE id IN (?)';
        db.query(query, [ids], callback);
    },

    toggleStoreStatus: (id, updated_by, callback) => {
        const query = 'UPDATE stores SET is_active = NOT is_active, updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
        db.query(query, [updated_by, id], callback);
    },

    getAllManagers: (callback) => {
        const query = `
            SELECT s.id, 
                   s.firstName, 
                   s.lastName,
                   s.employeeId,
                   s.email,
                   s.phoneNumber,
                   s.designationId,
                   s.departmentId,
                   s.isActive,
                   s.employmentStatus,
                   d.name AS designation_name,
                   dep.name AS department_name
            FROM staff s
            INNER JOIN designations d ON s.designationId = d.id
            INNER JOIN departments dep ON s.departmentId = dep.id
            WHERE s.isActive = true 
            AND d.name LIKE '%manager%'
            AND s.employmentStatus = 'Active'
            ORDER BY s.firstName, s.lastName
        `;
        db.query(query, callback);
    },

    // Add these methods to masterModel.js

    // Table creation method
    createStaffStoreTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS staff_store_mapping (
                id INT AUTO_INCREMENT PRIMARY KEY,
                staff_id INT NOT NULL,
                store_id INT NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (staff_id) REFERENCES staff(id),
                FOREIGN KEY (store_id) REFERENCES stores(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id),
                UNIQUE KEY unique_staff_store (staff_id, store_id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating staff_store_mapping table:', err);
            } else {
                console.log('Staff store mapping table ensured');
            }
        });
    },

    // Add methods for staff-store mapping
    assignStaffToStore: (mappingData, callback) => {
        const query = `
            INSERT INTO staff_store_mapping 
            (staff_id, store_id, created_by, updated_by) 
            VALUES (?, ?, ?, ?)
        `;
        
        const values = [
            mappingData.staff_id,
            mappingData.store_id,
            mappingData.created_by,
            mappingData.created_by
        ];

        db.query(query, values, callback);
    },

    // Method for bulk assignment
    bulkAssignStaffToStore: (mappings, callback) => {
        const query = `
            INSERT INTO staff_store_mapping 
            (staff_id, store_id, created_by, updated_by) 
            VALUES ?
        `;
        
        // Format the mappings data
        const values = mappings.map(m => [
            m.staff_id,
            m.store_id,
            m.created_by,
            m.created_by
        ]);

        db.query(query, [values], callback);
    },

    // Get all staff assigned to a store
    // getStoreStaff: (store_id, callback) => {
    //     const query = `
    //         SELECT s.*, ssm.id as mapping_id, ssm.is_active as mapping_active,
    //                d.name as designation_name,
    //                u1.username as created_by_username,
    //                u2.username as updated_by_username
    //         FROM staff_store_mapping ssm
    //         JOIN staff s ON ssm.staff_id = s.id
    //         LEFT JOIN designations d ON s.designationId = d.id
    //         LEFT JOIN users u1 ON ssm.created_by = u1.id
    //         LEFT JOIN users u2 ON ssm.updated_by = u2.id
    //         WHERE ssm.store_id = ?
    //     `;
    //     db.query(query, [store_id], callback);
    // },

    getStoreStaff: (storeId, callback) => {
        const query = `
            SELECT 
                s.*,
                ssm.id as mapping_id,
                ssm.is_active as mapping_active,
                ssm.store_id,
                d.name as designation_name,
                dep.name as department_name,
                u.username as created_by_username,
                ssm.created_at,
                ssm.updated_at
            FROM staff s
            INNER JOIN staff_store_mapping ssm ON s.id = ssm.staff_id
            LEFT JOIN designations d ON s.designationId = d.id
            LEFT JOIN departments dep ON s.departmentId = dep.id
            LEFT JOIN users u ON ssm.created_by = u.id
            WHERE ssm.store_id = ?
            ORDER BY s.firstName, s.lastName
        `;
        
        db.query(query, [storeId], callback);
    },

    // Get all stores assigned to a staff member
    getStaffStores: (staff_id, callback) => {
        const query = `
            SELECT st.*, ssm.id as mapping_id, ssm.is_active as mapping_active,
                   u1.username as created_by_username,
                   u2.username as updated_by_username
            FROM staff_store_mapping ssm
            JOIN stores st ON ssm.store_id = st.id
            LEFT JOIN users u1 ON ssm.created_by = u1.id
            LEFT JOIN users u2 ON ssm.updated_by = u2.id
            WHERE ssm.staff_id = ?
        `;
        db.query(query, [staff_id], callback);
    },

    // Check if a specific mapping exists
    getSpecificStaffStoreMapping: (staff_id, store_id, callback) => {
        const query = `
            SELECT * FROM staff_store_mapping 
            WHERE staff_id = ? AND store_id = ?
        `;
        db.query(query, [staff_id, store_id], callback);
    },

    // Remove a staff member from a store
    removeStaffFromStore: (staff_id, store_id, callback) => {
        const query = 'DELETE FROM staff_store_mapping WHERE staff_id = ? AND store_id = ?';
        db.query(query, [staff_id, store_id], callback);
    },

    // Update the active status of a staff-store mapping
    updateStaffStoreStatus: (mapping_id, is_active, updated_by, callback) => {
        const query = `
            UPDATE staff_store_mapping 
            SET is_active = ?, 
                updated_by = ?, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        db.query(query, [is_active, updated_by, mapping_id], callback);
    },

    // Validation helper
    validateStaffStoreMapping: (staff_id, store_id, callback) => {
        const staffQuery = 'SELECT id FROM staff WHERE id = ?';
        const storeQuery = 'SELECT id FROM stores WHERE id = ?';
        
        // Check if staff exists
        db.query(staffQuery, [staff_id], (err, staffResults) => {
            if (err) return callback(err);
            if (staffResults.length === 0) return callback('Staff not found');

            // Check if store exists
            db.query(storeQuery, [store_id], (err, storeResults) => {
                if (err) return callback(err);
                if (storeResults.length === 0) return callback('Store not found');

                callback(null, true);
            });
        });
    },

    getUnassignedStaff: (callback) => {
        const query = `
            SELECT s.*, 
                d.name as designation_name,
                dep.name as department_name
            FROM staff s
            LEFT JOIN designations d ON s.designationId = d.id
            LEFT JOIN departments dep ON s.departmentId = dep.id
            LEFT JOIN staff_store_mapping ssm ON s.id = ssm.staff_id
            WHERE s.isActive = true
            AND s.employmentStatus = 'Active'
            AND (ssm.id IS NULL OR ssm.is_active = false)
            AND d.name NOT LIKE '%manager%'
            ORDER BY s.firstName, s.lastName
        `;
        db.query(query, callback);
     },

    // Get active mappings count for a store
    getStoreStaffCount: (store_id, callback) => {
        const query = `
            SELECT COUNT(*) as count
            FROM staff_store_mapping
            WHERE store_id = ? AND is_active = true
        `;
        db.query(query, [store_id], callback);
    },

    // Get active mappings count for a staff member
    getStaffStoreCount: (staff_id, callback) => {
        const query = `
            SELECT COUNT(*) as count
            FROM staff_store_mapping
            WHERE staff_id = ? AND is_active = true
        `;
        db.query(query, [staff_id], callback);
    },

    bulkRemoveStaffFromStore: (mappingIds, callback) => {
        const query = 'DELETE FROM staff_store_mapping WHERE id IN (?)';
        db.query(query, [mappingIds], callback);
    },








};

// Ensure the tables are created when the application starts
Master.createStateTable();
Master.createCityTable();
Master.createStoreTable();
Master.createBloodGroupTable();
Master.createReligionTable();
Master.createCommunityTable();
Master.createDesignationTable();
Master.createDepartmentTable();
Master.createEmploymentTypeTable();
Master.createGenderTable();
Master.createStoreTable();
Master.createStaffStoreTable();

module.exports = Master;


