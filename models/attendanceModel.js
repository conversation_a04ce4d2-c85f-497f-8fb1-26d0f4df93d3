const db = require('../config/database');

const Attendance = {
    // Create attendance table if it doesn't exist
    createAttendanceTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS attendance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                staff_id INT NOT NULL,
                store_id INT NOT NULL,
                date DATE NOT NULL,
                status ENUM('present', 'absent') NOT NULL DEFAULT 'present',
                absence_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                updated_by INT,
                FOREIGN KEY (staff_id) REFERENCES staff(id),
                FOREIGN KEY (store_id) REFERENCES stores(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (updated_by) REFERENCES users(id),
                UNIQUE KEY unique_attendance (staff_id, date)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating attendance table:', err);
            } else {
                console.log('Attendance table ensured');
            }
        });
    },

    // Mark attendance for multiple staff members
    markAttendance: (attendanceData, callback) => {
        const query = `
            INSERT INTO attendance 
            (staff_id, store_id, date, status, absence_reason, created_by) 
            VALUES ? 
            ON DUPLICATE KEY UPDATE 
            status = VALUES(status),
            absence_reason = VALUES(absence_reason),
            updated_by = VALUES(created_by),
            updated_at = CURRENT_TIMESTAMP
        `;
        db.query(query, [attendanceData], callback);
    },

    // Get attendance by store and date
    getAttendanceByStoreAndDate: (storeId, date, callback) => {
        const query = `
            SELECT a.*, 
                   s.firstName, s.lastName,
                   u1.username as created_by_username,
                   u2.username as updated_by_username
            FROM attendance a
            LEFT JOIN staff s ON a.staff_id = s.id
            LEFT JOIN users u1 ON a.created_by = u1.id
            LEFT JOIN users u2 ON a.updated_by = u2.id
            WHERE a.store_id = ? AND a.date = ?
        `;
        db.query(query, [storeId, date], callback);
    },

    // Get attendance history for a staff member
    getStaffAttendanceHistory: (staffId, startDate, endDate, callback) => {
        const query = `
            SELECT a.*, 
                   u1.username as created_by_username,
                   u2.username as updated_by_username
            FROM attendance a
            LEFT JOIN users u1 ON a.created_by = u1.id
            LEFT JOIN users u2 ON a.updated_by = u2.id
            WHERE a.staff_id = ? 
            AND a.date BETWEEN ? AND ?
            ORDER BY a.date DESC
        `;
        db.query(query, [staffId, startDate, endDate], callback);
    },

    // Get attendance summary for a store
    getStoreSummary: (storeId, startDate, endDate, callback) => {
        const query = `
            SELECT 
                a.date,
                COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count
            FROM attendance a
            WHERE a.store_id = ? 
            AND a.date BETWEEN ? AND ?
            GROUP BY a.date
            ORDER BY a.date DESC
        `;
        db.query(query, [storeId, startDate, endDate], callback);
    }
};

// Create table when the application starts
Attendance.createAttendanceTable();

module.exports = Attendance;