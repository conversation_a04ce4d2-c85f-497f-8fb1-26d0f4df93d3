// models/auth/authModule.model.js
const db = require('../config/database');

const Auth = {
    createTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS auth_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                action VARCHAR(50) NOT NULL,
                action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status BOOLEAN DEFAULT true,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        `;
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating auth_log table:', err);
            } else {
                console.log('Auth_log table ensured');
            }
        });
    },

    validateSession: (userId, callback) => {
        const query = 'SELECT is_active FROM users WHERE id = ?';
        db.query(query, [userId], callback);
    },

    logAuthAction: (userId, action, status, callback) => {
        const query = 'INSERT INTO auth_log (user_id, action, status) VALUES (?, ?, ?)';
        db.query(query, [userId, action, status], callback);
    }
};

Auth.createTable();

module.exports = Auth;