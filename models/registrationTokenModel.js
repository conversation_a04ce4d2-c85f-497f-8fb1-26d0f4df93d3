// models/registrationTokenModel.js

const db = require('../config/database');
const crypto = require('crypto');

const RegistrationToken = {
    // Create the registration tokens table
    createTable: () => {
        const query = `
            CREATE TABLE IF NOT EXISTS registration_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                token VARCHAR(255) NOT NULL UNIQUE,
                position_id INT,
                email VARCHAR(255),
                expiry_date DATETIME NOT NULL,
                is_used BOOLEAN DEFAULT FALSE,
                is_valid BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INT NOT NULL,
                FOREIGN KEY (position_id) REFERENCES positions(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `;
        
        db.query(query, (err) => {
            if (err) {
                console.error('Error creating registration_tokens table:', err);
            } else {
                console.log('Registration tokens table created or already exists');
            }
        });
    },
    
    // Generate a new registration token
    generateToken: (data, callback) => {
        // Generate a random token
        const token = crypto.randomBytes(32).toString('hex');
        
        // Set expiry date (default: 7 days from now)
        const expiryDays = data.expiry_days || 7;
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + expiryDays);
        
        const tokenData = {
            token,
            position_id: data.position_id || null,
            email: data.email || null,
            expiry_date: expiryDate,
            created_by: data.created_by
        };
        
        const query = 'INSERT INTO registration_tokens SET ?';
        db.query(query, tokenData, callback);
    },
    
    // Validate a token
    validateToken: (token, callback) => {
        const query = `
            SELECT * FROM registration_tokens 
            WHERE token = ? 
            AND expiry_date > NOW() 
            AND is_used = FALSE 
            AND is_valid = TRUE
        `;
        db.query(query, [token], callback);
    },
    
    // Mark a token as used
    markTokenAsUsed: (token, callback) => {
        const query = 'UPDATE registration_tokens SET is_used = TRUE WHERE token = ?';
        db.query(query, [token], callback);
    },
    
    // Invalidate a token
    invalidateToken: (token, callback) => {
        const query = 'UPDATE registration_tokens SET is_valid = FALSE WHERE token = ?';
        db.query(query, [token], callback);
    },
    
    // Get all tokens
    getAllTokens: (callback) => {
        const query = `
            SELECT rt.*, 
                   p.title AS position_title,
                   u.username AS created_by_username
            FROM registration_tokens rt
            LEFT JOIN positions p ON rt.position_id = p.id
            LEFT JOIN users u ON rt.created_by = u.id
            ORDER BY rt.created_at DESC
        `;
        db.query(query, callback);
    },
    
    // Get tokens by user
    getTokensByUser: (userId, callback) => {
        const query = `
            SELECT rt.*, 
                   p.title AS position_title,
                   u.username AS created_by_username
            FROM registration_tokens rt
            LEFT JOIN positions p ON rt.position_id = p.id
            LEFT JOIN users u ON rt.created_by = u.id
            WHERE rt.created_by = ?
            ORDER BY rt.created_at DESC
        `;
        db.query(query, [userId], callback);
    },
    
    // Delete a token
    deleteToken: (tokenId, callback) => {
        const query = 'DELETE FROM registration_tokens WHERE id = ?';
        db.query(query, [tokenId], callback);
    }
};

// Create the table when the application starts
RegistrationToken.createTable();

module.exports = RegistrationToken;
