const db = require('../config/database');

const Module = {
    createTable: () => {
        const createModulesTableQuery = `
            CREATE TABLE IF NOT EXISTS modules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;

        const createRoleModulePermissionsTableQuery = `
           CREATE TABLE IF NOT EXISTS role_module_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    module_id INT NOT NULL,
    permissions JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (module_id) REFERENCES modules(id)
);
        `;

        db.query(createModulesTableQuery, (err) => {
            if (err) {
                console.error('Error creating modules table:', err);
            } else {
                console.log('Modules table ensured');
            }
        });

        db.query(createRoleModulePermissionsTableQuery, (err) => {
            if (err) {
                console.error('Error creating role module permissions table:', err);
            } else {
                console.log('Role module permissions table ensured');
            }
        });
    },

    findByName: (name, callback) => {
        const query = 'SELECT * FROM modules WHERE name = ?';
        db.query(query, [name], (err, results) => {
            if (err) {
                return callback(err);
            }
            if (results.length > 0) {
                return callback(null, results[0]);
            } else {
                return callback(null, null);
            }
        });
    },

    createModule: ({ name }, callback) => {
        const query = 'INSERT INTO modules (name) VALUES (?)';
        db.query(query, [name], (err, results) => {
            if (err) {
                return callback(err);
            }
            callback(null, { id: results.insertId, name });
        });
    },

    getModules: (callback) => {
        const query = 'SELECT * FROM modules';
        db.query(query, (err, results) => {
            if (err) {
                return callback(err);
            }
            callback(null, results);
        });
    },

    getRolePermissions: (roleId, callback) => {
        const query = `
            SELECT m.name AS module, rmp.permissions
            FROM role_module_permissions rmp
            JOIN modules m ON rmp.module_id = m.id
            WHERE rmp.role_id = ?
        `;
        db.query(query, [roleId], (err, results) => {
            if (err) {
                return callback(err);
            }
            callback(null, results);
        });
    },

    setRolePermissions: ({ roleId, moduleId, permissions }, callback) => {
        const query = `
            INSERT INTO role_module_permissions (role_id, module_id, permissions)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE permissions = ?
        `;
        db.query(query, [roleId, moduleId, JSON.stringify(permissions), JSON.stringify(permissions)], (err) => {
            if (err) {
                return callback(err);
            }
            callback(null);
        });
    },

    deleteModule: (moduleId, callback) => {
        const deletePermissionsQuery = 'DELETE FROM role_module_permissions WHERE module_id = ?';
        db.query(deletePermissionsQuery, [moduleId], (err) => {
            if (err) {
                return callback(err);
            }
            const deleteModuleQuery = 'DELETE FROM modules WHERE id = ?';
            db.query(deleteModuleQuery, [moduleId], (err) => {
                if (err) {
                    return callback(err);
                }
                callback(null);
            });
        });
    },

    updateModuleName: (id, name, callback) => {
        const updateModuleQuery = 'UPDATE modules SET name = ? WHERE id = ?';
        db.query(updateModuleQuery, [name, id], (err) => {
            if (err) {
                return callback(err);
            }
            callback(null);
        });
    },

    updateRoleModulePermissions: ({ roleId, moduleId, permissions }, callback) => {
        const query = `
            UPDATE role_module_permissions
            SET permissions = ?
            WHERE role_id = ? AND module_id = ?
        `;
        db.query(query, [JSON.stringify(permissions), roleId, moduleId], (err) => {
            if (err) {
                return callback(err);
            }
            callback(null);
        });
    }
};

// Ensure the table is created when the application starts
Module.createTable();

module.exports = Module;