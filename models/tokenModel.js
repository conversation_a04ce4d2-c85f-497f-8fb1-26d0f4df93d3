const db = require('../config/database');

const Token = {
    create: (userId, token, callback) => {
        const query = 'INSERT INTO user_tokens (user_id, token) VALUES (?, ?)';
        db.query(query, [userId, token], callback);
    },
    findByToken: (token, callback) => {
        const query = 'SELECT * FROM user_tokens WHERE token = ?';
        db.query(query, [token], callback);
    }
};

module.exports = Token;
