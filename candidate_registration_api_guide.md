# API Integration Guide for Candidate Registration System

## Public API Endpoints

### 1. Validate Registration Token
```
GET /public/validate-token/:token
```
- **Purpose**: Validates if a registration token is valid and active
- **Response**: Returns token details and associated position if any
- **Example Response**:
```json
{
  "success": true,
  "token": {
    "id": 1,
    "token": "abc123...",
    "position_id": 5,
    "email": "<EMAIL>",
    "expiry_date": "2023-06-30T00:00:00.000Z",
    "is_used": false,
    "is_valid": true
  },
  "position": {
    "id": 5,
    "title": "Software Engineer",
    "department_id": 3,
    "required_skills": "JavaScript, Node.js",
    "required_qualifications": "Bachelor's degree"
  },
  "message": "Valid registration token"
}
```

### 2. Submit Candidate Registration
```
POST /public/candidate-registration/:token
```
- **Purpose**: Registers a new candidate using a valid token
- **Content-Type**: `multipart/form-data` (for file uploads)
- **Request Body**:
  - All candidate fields (see Candidate Data Structure below)
  - File uploads: `resumeFile` and `profilePicture`
- **Example Response**:
```json
{
  "success": true,
  "message": "Registration successful",
  "candidateId": 42
}
```

### 3. Get Open Positions
```
GET /public/open-positions
```
- **Purpose**: Retrieves all open positions for public viewing
- **Response**: List of available positions
- **Example Response**:
```json
{
  "success": true,
  "positions": [
    {
      "id": 5,
      "title": "Software Engineer",
      "department_id": 3,
      "department_name": "Engineering",
      "required_skills": "JavaScript, Node.js",
      "vacancies": 2,
      "status": "Open"
    }
  ]
}
```

## Admin API Endpoints (For Token Management)

### 1. Generate Registration Token
```
POST /interview/registration-token
```
- **Purpose**: Creates a new registration token
- **Request Body**:
```json
{
  "position_id": 5,  // Optional
  "email": "<EMAIL>",  // Optional
  "expiry_days": 7  // Optional, defaults to 7
}
```
- **Response**: Details of the created token

### 2. Get All Registration Tokens
```
GET /interview/registration-tokens
```
- **Purpose**: Retrieves all registration tokens
- **Response**: List of all tokens with their details

### 3. Get My Registration Tokens
```
GET /interview/my-registration-tokens
```
- **Purpose**: Retrieves tokens created by the current user
- **Response**: List of tokens created by the user

### 4. Invalidate a Token
```
PATCH /interview/registration-token/:token/invalidate
```
- **Purpose**: Invalidates a token so it can no longer be used
- **Response**: Confirmation of invalidation

### 5. Delete a Token
```
DELETE /interview/registration-token/:id
```
- **Purpose**: Permanently deletes a token
- **Response**: Confirmation of deletion

## Candidate Data Structure

When submitting a candidate registration, include these fields:

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1990-01-01",
  "genderId": 1,
  "email": "<EMAIL>",
  "phoneNumber": "1234567890",
  "alternatePhone": "0987654321",
  "address": "123 Main St",
  "permanentAddress": "123 Main St",
  "city": "Anytown",
  "state": "State",
  "postalCode": "12345",
  "educationLevel": "Bachelor's",
  "degrees": "Computer Science",
  "university": "Example University",
  "yearOfPassing": 2015,
  "specialization": "Software Engineering",
  "additionalCertifications": "AWS Certified Developer",
  "totalExperience": 5.5,
  "currentOrganization": "Current Company",
  "currentDesignation": "Senior Developer",
  "currentSalary": 75000,
  "expectedSalary": 90000,
  "noticePeriod": 30,
  "reasonForChange": "Career growth",
  "skills": "JavaScript, Node.js, React, Express",
  "bloodGroupId": 1,
  "religion": "Optional",
  "community": "Optional",
  "father_name": "Father Name",
  "father_occupation": "Occupation",
  "father_contact": "Contact Number",
  "father_status": "Living",
  "mother_name": "Mother Name",
  "mother_occupation": "Occupation",
  "mother_contact": "Contact Number",
  "mother_status": "Living",
  "family_address": "Family Address",
  "siblings": [
    {
      "name": "Sibling Name",
      "age": 25,
      "occupation": "Occupation",
      "contact": "Contact Number"
    }
  ]
}
```

## File Upload Requirements

1. **Resume File**:
   - Field name: `resumeFile`
   - Allowed formats: PDF, DOC, DOCX
   - Maximum size: 5MB

2. **Profile Picture**:
   - Field name: `profilePicture`
   - Allowed formats: JPG, PNG, GIF
   - Maximum size: 3MB

## Integration Steps

1. **Frontend Form Creation**:
   - Create a form that collects all required candidate information
   - Include file upload fields for resume and profile picture
   - Implement client-side validation

2. **Token Validation**:
   - Extract token from URL (e.g., from query parameter or path)
   - Call the validate-token endpoint to verify token validity
   - If valid, show the registration form; otherwise, show an error

3. **Form Submission**:
   - Submit the form data as `multipart/form-data`
   - Include all required fields and file uploads
   - Handle success and error responses appropriately

4. **Admin Token Management**:
   - Create interfaces for administrators to generate and manage tokens
   - Implement token sharing functionality (e.g., via email)

## Example Frontend Implementation

Here's a simple HTML form example for candidate registration:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Candidate Registration</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Candidate Registration</h1>
        <div id="token-status"></div>
        
        <form id="registration-form" enctype="multipart/form-data">
            <h2>Personal Information</h2>
            <div class="form-group">
                <label for="firstName">First Name*</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name*</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <!-- Add all other fields as needed -->
            
            <div class="form-group">
                <label for="resumeFile">Resume (PDF, DOC, DOCX)*</label>
                <input type="file" id="resumeFile" name="resumeFile" accept=".pdf,.doc,.docx" required>
            </div>
            
            <div class="form-group">
                <label for="profilePicture">Profile Picture (JPG, PNG, GIF)</label>
                <input type="file" id="profilePicture" name="profilePicture" accept=".jpg,.jpeg,.png,.gif">
            </div>
            
            <button type="submit">Submit Application</button>
        </form>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
```

And the corresponding JavaScript:

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Extract token from URL
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (!token) {
        document.getElementById('token-status').innerHTML = 
            '<div class="error">No registration token provided. Please use the link sent to your email.</div>';
        document.getElementById('registration-form').style.display = 'none';
        return;
    }
    
    // Validate token
    fetch(`/public/validate-token/${token}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('token-status').innerHTML = 
                    '<div class="success">Valid registration link. Please complete the form below.</div>';
                
                // If position is associated, show position details
                if (data.position) {
                    document.getElementById('token-status').innerHTML += 
                        `<div class="position-info">You are applying for: <strong>${data.position.title}</strong></div>`;
                }
            } else {
                document.getElementById('token-status').innerHTML = 
                    '<div class="error">Invalid or expired registration link.</div>';
                document.getElementById('registration-form').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error validating token:', error);
            document.getElementById('token-status').innerHTML = 
                '<div class="error">Error validating registration link. Please try again later.</div>';
            document.getElementById('registration-form').style.display = 'none';
        });
    
    // Handle form submission
    document.getElementById('registration-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch(`/public/candidate-registration/${token}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('registration-form').innerHTML = 
                    '<div class="success-message">' +
                    '<h2>Registration Successful!</h2>' +
                    '<p>Thank you for submitting your application. We will review your information and contact you soon.</p>' +
                    '</div>';
            } else {
                alert('Error submitting form: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            alert('Error submitting form. Please try again later.');
        });
    });
});
```
