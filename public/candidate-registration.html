<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Candidate Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .container {
            max-width: 800px;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 30px;
            text-align: center;
        }
        h2 {
            color: #0d6efd;
            font-size: 1.5rem;
            margin-top: 30px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .form-label {
            font-weight: 500;
        }
        .required:after {
            content: " *";
            color: red;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            padding: 10px 30px;
            font-size: 1.1rem;
            margin-top: 20px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c2c7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            color: #0f5132;
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .position-info {
            background-color: #e2f0fd;
            border: 1px solid #cfe2ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            color: #084298;
        }
        .success-message {
            text-align: center;
            padding: 40px 20px;
        }
        .success-message h2 {
            color: #0f5132;
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Candidate Registration</h1>
        <div id="token-status"></div>
        
        <form id="registration-form" enctype="multipart/form-data">
            <h2>Personal Information</h2>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="firstName" class="form-label required">First Name</label>
                    <input type="text" class="form-control" id="firstName" name="firstName" required>
                </div>
                <div class="col-md-6">
                    <label for="lastName" class="form-label required">Last Name</label>
                    <input type="text" class="form-control" id="lastName" name="lastName" required>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="dateOfBirth" class="form-label">Date of Birth</label>
                    <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth">
                </div>
                <div class="col-md-6">
                    <label for="genderId" class="form-label">Gender</label>
                    <select class="form-select" id="genderId" name="genderId">
                        <option value="">Select Gender</option>
                        <option value="1">Male</option>
                        <option value="2">Female</option>
                        <option value="3">Other</option>
                    </select>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="email" class="form-label required">Email</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                </div>
                <div class="col-md-6">
                    <label for="phoneNumber" class="form-label required">Phone Number</label>
                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="alternatePhone" class="form-label">Alternate Phone</label>
                <input type="tel" class="form-control" id="alternatePhone" name="alternatePhone">
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label">Current Address</label>
                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
            </div>
            
            <div class="mb-3">
                <label for="permanentAddress" class="form-label">Permanent Address</label>
                <textarea class="form-control" id="permanentAddress" name="permanentAddress" rows="3"></textarea>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-4">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" id="city" name="city">
                </div>
                <div class="col-md-4">
                    <label for="state" class="form-label">State</label>
                    <input type="text" class="form-control" id="state" name="state">
                </div>
                <div class="col-md-4">
                    <label for="postalCode" class="form-label">Postal Code</label>
                    <input type="text" class="form-control" id="postalCode" name="postalCode">
                </div>
            </div>
            
            <h2>Education Details</h2>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="educationLevel" class="form-label">Education Level</label>
                    <input type="text" class="form-control" id="educationLevel" name="educationLevel">
                </div>
                <div class="col-md-6">
                    <label for="degrees" class="form-label">Degrees</label>
                    <input type="text" class="form-control" id="degrees" name="degrees">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="university" class="form-label">University/Institution</label>
                    <input type="text" class="form-control" id="university" name="university">
                </div>
                <div class="col-md-6">
                    <label for="yearOfPassing" class="form-label">Year of Passing</label>
                    <input type="number" class="form-control" id="yearOfPassing" name="yearOfPassing">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="specialization" class="form-label">Specialization</label>
                <input type="text" class="form-control" id="specialization" name="specialization">
            </div>
            
            <div class="mb-3">
                <label for="additionalCertifications" class="form-label">Additional Certifications</label>
                <textarea class="form-control" id="additionalCertifications" name="additionalCertifications" rows="2"></textarea>
            </div>
            
            <h2>Professional Experience</h2>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="totalExperience" class="form-label">Total Experience (years)</label>
                    <input type="number" step="0.1" class="form-control" id="totalExperience" name="totalExperience">
                </div>
                <div class="col-md-6">
                    <label for="currentOrganization" class="form-label">Current Organization</label>
                    <input type="text" class="form-control" id="currentOrganization" name="currentOrganization">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="currentDesignation" class="form-label">Current Designation</label>
                    <input type="text" class="form-control" id="currentDesignation" name="currentDesignation">
                </div>
                <div class="col-md-6">
                    <label for="currentSalary" class="form-label">Current Salary</label>
                    <input type="number" class="form-control" id="currentSalary" name="currentSalary">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="expectedSalary" class="form-label">Expected Salary</label>
                    <input type="number" class="form-control" id="expectedSalary" name="expectedSalary">
                </div>
                <div class="col-md-6">
                    <label for="noticePeriod" class="form-label">Notice Period (days)</label>
                    <input type="number" class="form-control" id="noticePeriod" name="noticePeriod">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="reasonForChange" class="form-label">Reason for Change</label>
                <textarea class="form-control" id="reasonForChange" name="reasonForChange" rows="2"></textarea>
            </div>
            
            <div class="mb-3">
                <label for="skills" class="form-label">Skills</label>
                <textarea class="form-control" id="skills" name="skills" rows="2"></textarea>
            </div>
            
            <h2>Family Information</h2>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="father_name" class="form-label">Father's Name</label>
                    <input type="text" class="form-control" id="father_name" name="father_name">
                </div>
                <div class="col-md-6">
                    <label for="father_occupation" class="form-label">Father's Occupation</label>
                    <input type="text" class="form-control" id="father_occupation" name="father_occupation">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="father_contact" class="form-label">Father's Contact</label>
                    <input type="tel" class="form-control" id="father_contact" name="father_contact">
                </div>
                <div class="col-md-6">
                    <label for="father_status" class="form-label">Father's Status</label>
                    <select class="form-select" id="father_status" name="father_status">
                        <option value="Living">Living</option>
                        <option value="Deceased">Deceased</option>
                    </select>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="mother_name" class="form-label">Mother's Name</label>
                    <input type="text" class="form-control" id="mother_name" name="mother_name">
                </div>
                <div class="col-md-6">
                    <label for="mother_occupation" class="form-label">Mother's Occupation</label>
                    <input type="text" class="form-control" id="mother_occupation" name="mother_occupation">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="mother_contact" class="form-label">Mother's Contact</label>
                    <input type="tel" class="form-control" id="mother_contact" name="mother_contact">
                </div>
                <div class="col-md-6">
                    <label for="mother_status" class="form-label">Mother's Status</label>
                    <select class="form-select" id="mother_status" name="mother_status">
                        <option value="Living">Living</option>
                        <option value="Deceased">Deceased</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="family_address" class="form-label">Family Address</label>
                <textarea class="form-control" id="family_address" name="family_address" rows="3"></textarea>
            </div>
            
            <h2>Documents</h2>
            <div class="mb-3">
                <label for="resumeFile" class="form-label required">Resume (PDF, DOC, DOCX)</label>
                <input type="file" class="form-control" id="resumeFile" name="resumeFile" accept=".pdf,.doc,.docx" required>
            </div>
            
            <div class="mb-3">
                <label for="profilePicture" class="form-label">Profile Picture (JPG, PNG, GIF)</label>
                <input type="file" class="form-control" id="profilePicture" name="profilePicture" accept=".jpg,.jpeg,.png,.gif">
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Submit Application</button>
            </div>
        </form>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Extract token from URL
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            
            if (!token) {
                document.getElementById('token-status').innerHTML = 
                    '<div class="error">No registration token provided. Please use the link sent to your email.</div>';
                document.getElementById('registration-form').style.display = 'none';
                return;
            }
            
            // Validate token
            fetch(`/public/validate-token/${token}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('token-status').innerHTML = 
                            '<div class="success">Valid registration link. Please complete the form below.</div>';
                        
                        // If position is associated, show position details
                        if (data.position) {
                            document.getElementById('token-status').innerHTML += 
                                `<div class="position-info">You are applying for: <strong>${data.position.title}</strong></div>`;
                        }
                    } else {
                        document.getElementById('token-status').innerHTML = 
                            '<div class="error">Invalid or expired registration link.</div>';
                        document.getElementById('registration-form').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error validating token:', error);
                    document.getElementById('token-status').innerHTML = 
                        '<div class="error">Error validating registration link. Please try again later.</div>';
                    document.getElementById('registration-form').style.display = 'none';
                });
            
            // Handle form submission
            document.getElementById('registration-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                
                fetch(`/public/candidate-registration/${token}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('registration-form').innerHTML = 
                            '<div class="success-message">' +
                            '<h2>Registration Successful!</h2>' +
                            '<p>Thank you for submitting your application. We will review your information and contact you soon.</p>' +
                            '</div>';
                    } else {
                        alert('Error submitting form: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error submitting form:', error);
                    alert('Error submitting form. Please try again later.');
                });
            });
        });
    </script>
</body>
</html>
