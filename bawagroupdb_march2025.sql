-- MySQL dump 10.13  Distrib 9.2.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: bawagroupdb
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `attendance`
--

DROP TABLE IF EXISTS `attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `store_id` int NOT NULL,
  `date` date NOT NULL,
  `status` enum('present','absent') NOT NULL DEFAULT 'present',
  `absence_reason` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_attendance` (`staff_id`,`date`),
  KEY `store_id` (`store_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`),
  CONSTRAINT `attendance_ibfk_2` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`),
  CONSTRAINT `attendance_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `attendance_ibfk_4` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `attendance`
--

LOCK TABLES `attendance` WRITE;
/*!40000 ALTER TABLE `attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_log`
--

DROP TABLE IF EXISTS `auth_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) NOT NULL,
  `action_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `auth_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_log`
--

LOCK TABLES `auth_log` WRITE;
/*!40000 ALTER TABLE `auth_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blood_groups`
--

DROP TABLE IF EXISTS `blood_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `blood_groups` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(10) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `blood_groups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `blood_groups_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blood_groups`
--

LOCK TABLES `blood_groups` WRITE;
/*!40000 ALTER TABLE `blood_groups` DISABLE KEYS */;
INSERT INTO `blood_groups` VALUES (21,'O+','2025-01-08 12:50:31','2025-01-08 12:50:31',22,22),(22,'O-','2025-01-08 12:50:42','2025-01-08 12:50:42',22,22),(23,'A+','2025-01-08 12:50:50','2025-01-08 12:50:50',22,22),(24,'A-','2025-01-08 12:50:58','2025-01-08 12:50:58',22,22),(25,'B+','2025-01-08 12:51:08','2025-01-08 12:51:08',22,22),(26,'B-','2025-01-08 12:51:15','2025-01-08 12:51:15',22,22),(27,'AB+','2025-01-08 12:51:30','2025-01-08 12:51:30',22,22),(28,'AB-','2025-01-08 12:51:36','2025-01-08 12:51:36',22,22),(29,'A1+','2025-01-20 13:26:24','2025-01-20 13:26:24',22,22);
/*!40000 ALTER TABLE `blood_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cities`
--

DROP TABLE IF EXISTS `cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `state_id` (`state_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `cities_ibfk_1` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`),
  CONSTRAINT `cities_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `cities_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cities`
--

LOCK TABLES `cities` WRITE;
/*!40000 ALTER TABLE `cities` DISABLE KEYS */;
INSERT INTO `cities` VALUES (51,35,'Tiruppur','2025-01-08 12:52:05','2025-01-08 12:52:05',22,NULL),(52,35,'Sulur','2025-01-08 12:52:17','2025-01-08 12:52:17',22,NULL),(53,35,'Pallapatti','2025-01-08 13:23:35','2025-01-08 13:23:35',22,NULL),(54,35,'Kadayanallur','2025-01-08 13:23:44','2025-01-08 13:23:44',22,NULL),(55,35,'Viluppuram','2025-01-15 05:54:38','2025-01-15 05:54:38',22,NULL),(56,35,'Salem','2025-01-20 13:21:34','2025-01-20 13:21:34',22,NULL),(57,35,'Tenkasi','2025-01-20 13:21:49','2025-01-20 13:21:49',22,NULL);
/*!40000 ALTER TABLE `cities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `communities`
--

DROP TABLE IF EXISTS `communities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `communities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `communities_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `communities_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `communities`
--

LOCK TABLES `communities` WRITE;
/*!40000 ALTER TABLE `communities` DISABLE KEYS */;
INSERT INTO `communities` VALUES (12,'Not applicable','2025-01-13 14:50:04','2025-01-13 14:50:04',22,22);
/*!40000 ALTER TABLE `communities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `departments`
--

DROP TABLE IF EXISTS `departments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `departments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `departments_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `departments_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `departments`
--

LOCK TABLES `departments` WRITE;
/*!40000 ALTER TABLE `departments` DISABLE KEYS */;
INSERT INTO `departments` VALUES (25,'HR department',1,'2025-01-08 12:47:52','2025-01-08 12:47:52',22,22),(26,'Checking',1,'2025-01-08 12:48:00','2025-01-08 12:48:00',22,22),(27,'Inward checking',1,'2025-01-08 12:48:11','2025-01-08 12:48:11',22,22),(28,'Operations',1,'2025-01-08 12:48:29','2025-01-08 12:48:29',22,22),(29,'Store Manager',1,'2025-01-08 13:23:15','2025-01-13 14:52:10',22,6);
/*!40000 ALTER TABLE `departments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `designations`
--

DROP TABLE IF EXISTS `designations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `designations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `designations_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `designations_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `designations`
--

LOCK TABLES `designations` WRITE;
/*!40000 ALTER TABLE `designations` DISABLE KEYS */;
INSERT INTO `designations` VALUES (22,'Salesman',NULL,1,'2025-01-08 12:50:01','2025-01-08 12:50:01',22,22),(23,'Rackfilling',NULL,1,'2025-01-08 12:50:09','2025-01-08 12:50:09',22,22),(24,'manager',NULL,NULL,'2025-01-08 12:54:04','2025-01-13 14:48:04',22,22),(25,'Data Entry',NULL,1,'2025-01-16 12:03:55','2025-01-16 12:03:55',22,22),(26,'Surgical',NULL,1,'2025-01-16 12:04:03','2025-01-16 12:04:03',22,22);
/*!40000 ALTER TABLE `designations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employment_types`
--

DROP TABLE IF EXISTS `employment_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employment_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `employment_types_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `employment_types_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employment_types`
--

LOCK TABLES `employment_types` WRITE;
/*!40000 ALTER TABLE `employment_types` DISABLE KEYS */;
INSERT INTO `employment_types` VALUES (7,'Full time','Regular staffs',1,'2025-01-08 12:49:04','2025-01-08 12:49:04',22,22),(8,'Part time','College students',1,'2025-01-08 12:49:23','2025-01-08 12:49:23',22,22);
/*!40000 ALTER TABLE `employment_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `genders`
--

DROP TABLE IF EXISTS `genders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `genders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `genders_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `genders_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `genders`
--

LOCK TABLES `genders` WRITE;
/*!40000 ALTER TABLE `genders` DISABLE KEYS */;
INSERT INTO `genders` VALUES (5,'Male',1,'2025-01-08 12:49:36','2025-01-08 12:49:36',22,22),(6,'Female',1,'2025-01-08 12:49:42','2025-01-08 12:49:42',22,22);
/*!40000 ALTER TABLE `genders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `modules`
--

DROP TABLE IF EXISTS `modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `modules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modules`
--

LOCK TABLES `modules` WRITE;
/*!40000 ALTER TABLE `modules` DISABLE KEYS */;
INSERT INTO `modules` VALUES (1,'userModule','2024-06-28 18:19:54'),(3,'masterModule','2024-06-28 18:39:10'),(4,'moduleModule','2024-12-20 04:51:48'),(5,'authModule','2024-12-20 10:30:58');
/*!40000 ALTER TABLE `modules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `religions`
--

DROP TABLE IF EXISTS `religions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `religions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `religions_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `religions_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `religions`
--

LOCK TABLES `religions` WRITE;
/*!40000 ALTER TABLE `religions` DISABLE KEYS */;
INSERT INTO `religions` VALUES (6,'Hinduism','2025-01-08 13:06:10','2025-01-08 13:06:10',22,22),(7,'Islam','2025-01-08 13:06:18','2025-01-08 13:06:18',22,22),(8,'Christianity','2025-01-08 13:06:36','2025-01-08 13:06:36',22,22);
/*!40000 ALTER TABLE `religions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_module_permissions`
--

DROP TABLE IF EXISTS `role_module_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_module_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `module_id` int NOT NULL,
  `permissions` json NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `module_id` (`module_id`),
  CONSTRAINT `role_module_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `role_module_permissions_ibfk_2` FOREIGN KEY (`module_id`) REFERENCES `modules` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_module_permissions`
--

LOCK TABLES `role_module_permissions` WRITE;
/*!40000 ALTER TABLE `role_module_permissions` DISABLE KEYS */;
INSERT INTO `role_module_permissions` VALUES (1,1,1,'[\"read\", \"write\", \"update\", \"delete\"]','2024-06-28 18:32:36','2024-12-20 04:54:54'),(2,1,4,'[\"read\", \"write\", \"update\", \"delete\"]','2024-12-20 04:53:17','2024-12-20 04:54:54'),(3,1,3,'[\"read\", \"write\", \"update\", \"delete\"]','2024-12-20 04:56:12','2024-12-20 04:56:12'),(4,2,3,'[\"read\", \"write\", \"update\"]','2024-12-20 05:10:17','2024-12-20 05:10:17'),(6,2,5,'[\"read\"]','2024-12-20 10:31:08','2024-12-20 10:31:08');
/*!40000 ALTER TABLE `role_module_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'superadmin','2024-06-28 18:09:10'),(2,'hrmanager','2024-12-20 05:09:45');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `expires` int unsigned NOT NULL,
  `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  PRIMARY KEY (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `siblings`
--

DROP TABLE IF EXISTS `siblings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `siblings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `date_of_birth` date NOT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `occupation` varchar(255) DEFAULT NULL,
  `marital_status` varchar(50) DEFAULT NULL,
  `contact` varchar(20) DEFAULT NULL,
  `status` enum('Living','Deceased') DEFAULT 'Living',
  `is_emergency_contact` tinyint(1) DEFAULT '0',
  `additional_info` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `staff_id` (`staff_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `siblings_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE,
  CONSTRAINT `siblings_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `siblings_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `siblings`
--

LOCK TABLES `siblings` WRITE;
/*!40000 ALTER TABLE `siblings` DISABLE KEYS */;
/*!40000 ALTER TABLE `siblings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `staff`
--

DROP TABLE IF EXISTS `staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff` (
  `id` int NOT NULL AUTO_INCREMENT,
  `firstName` varchar(255) NOT NULL,
  `lastName` varchar(255) NOT NULL,
  `dateOfBirth` date NOT NULL,
  `genderId` int NOT NULL,
  `profilePicture` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `phoneNumber` varchar(20) NOT NULL,
  `emergencyContactName` varchar(255) NOT NULL,
  `emergencyContactNumber` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `city` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `postalCode` varchar(20) NOT NULL,
  `employeeId` varchar(50) DEFAULT NULL,
  `hireDate` date NOT NULL,
  `departmentId` int NOT NULL,
  `designationId` int NOT NULL,
  `employmentTypeId` int NOT NULL,
  `educationLevel` varchar(255) NOT NULL,
  `degrees` varchar(255) DEFAULT NULL,
  `salary` decimal(10,2) NOT NULL,
  `bloodGroupId` int NOT NULL,
  `religion` varchar(255) DEFAULT NULL,
  `community` varchar(255) DEFAULT NULL,
  `idProof` varchar(255) NOT NULL,
  `father_name` varchar(255) DEFAULT NULL,
  `isActive` tinyint(1) DEFAULT '1',
  `employmentStatus` enum('Active','On Leave','Terminated') DEFAULT 'Active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `father_occupation` varchar(255) DEFAULT NULL,
  `father_contact` varchar(20) DEFAULT NULL,
  `father_status` enum('Living','Deceased') DEFAULT 'Living',
  `mother_name` varchar(255) DEFAULT NULL,
  `mother_occupation` varchar(255) DEFAULT NULL,
  `mother_contact` varchar(20) DEFAULT NULL,
  `mother_status` enum('Living','Deceased') DEFAULT 'Living',
  `family_address` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `employeeId` (`employeeId`),
  KEY `genderId` (`genderId`),
  KEY `departmentId` (`departmentId`),
  KEY `designationId` (`designationId`),
  KEY `employmentTypeId` (`employmentTypeId`),
  KEY `bloodGroupId` (`bloodGroupId`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `staff_ibfk_1` FOREIGN KEY (`genderId`) REFERENCES `genders` (`id`),
  CONSTRAINT `staff_ibfk_2` FOREIGN KEY (`departmentId`) REFERENCES `departments` (`id`),
  CONSTRAINT `staff_ibfk_3` FOREIGN KEY (`designationId`) REFERENCES `designations` (`id`),
  CONSTRAINT `staff_ibfk_4` FOREIGN KEY (`employmentTypeId`) REFERENCES `employment_types` (`id`),
  CONSTRAINT `staff_ibfk_5` FOREIGN KEY (`bloodGroupId`) REFERENCES `blood_groups` (`id`),
  CONSTRAINT `staff_ibfk_6` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `staff_ibfk_7` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=118 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `staff`
--

LOCK TABLES `staff` WRITE;
/*!40000 ALTER TABLE `staff` DISABLE KEYS */;
INSERT INTO `staff` VALUES (54,'Mohamed Anshar Ali','P. S','1976-03-19',5,NULL,'<EMAIL>','9942778661','Shameem P.M','9976336074','pallapatti','53','35','639205','BG2025001','2012-07-22',29,24,7,'Bachelor\'s Degree','B.com',30000.00,21,'Islam','Not applicable','201050934293','EEEEEE',1,'Active','2025-01-13 14:42:12','2025-01-15 05:59:52',22,22,'BUSINESS','8888888888','Living','MMMMMMM','Housewife','7777777777','Living','pallapatti- 639205'),(86,'Sabana','begam','2002-07-16',6,'1736782679093-WhatsApp Image 2025-01-13 at 8.45.08 PM.jpeg','<EMAIL>','9345269556','Raja mohammed','9080690039','78, Sugumaran nagar west','51','35','641604','BG2025002','2025-01-13',28,23,8,'Diploma','D.pharm',5000.00,25,'','','490174752449','RAJA MOHAMMED',1,'Active','2025-01-13 15:37:59','2025-01-13 15:38:12',22,22,'Business','9080690039','Living','Anusath begam','Housewife','7845057996','Living','78, Sugumaran nagar west, Kangeyam cross road, tirupur'),(87,'Parameswari','A ','2005-01-06',6,'1736783398539-WhatsApp Image 2025-01-13 at 8.45.10 PM.jpeg','<EMAIL>','7540032185','Arumugam','9944132185','1/679 Thirukumaran nagar palavanjipalayam','51','35','641604','BG2025003','2024-02-19',28,23,8,'Bachelor\'s Degree','Bsc. Biotechnology',5000.00,23,'','','700371644846','Arumugam',1,'Active','2025-01-13 15:49:58','2025-01-15 05:56:08',22,22,'Tailor','9944132185','Living','Veni','Tailor','9999999999','Living','1/679 Thirukumaran nagar\r\npalavanjipalayam\r\ntirupur'),(88,'Jemila merlin mary','A ','2003-09-19',6,'1736921349509-WhatsApp Image 2024-02-23 at 5.30.16 PM.jpeg','<EMAIL>','6369359194','Antony samy','6384658287','123, Raja street, Yal, Maiyanur, Kallakurichi','55','35','605801','BG2025004','2023-01-23',28,23,8,'Master\'s Degree','M.Com',5000.00,25,'','','215144524864','Antony Samy',1,'Active','2025-01-15 06:09:09','2025-01-16 11:24:16',22,22,'Agriculture','6384658287','Living','Nirmala','House wife','9999999999','Living','123, Raja street, Yal, Maiyanur, Kallakurichi, Tamilnadu- 605801'),(89,'Mohammed Asif Ali','A. Z','2003-11-05',5,NULL,'<EMAIL>','9894702445','Zahir Hussain','9087503017','60, Periyakadai street, pallapatti','53','35','639205','BG2025005','2025-01-15',28,22,7,'High School','12th',14500.00,25,'','','476895026867','Zahir Hussain',1,'Active','2025-01-15 06:18:29','2025-01-16 11:24:35',22,22,'Business','9087503017','Living','Sajitha begam','House wife','9087503017','Living','60, Periyakadai street, pallapatti, Karur - 639205'),(90,'Archana','A ','2001-09-09',6,'1736923278574-archana.png','<EMAIL>','9952221001','Kalaimani','8220850411','5, R.V.E nagar 8th street, Rakkiyapalayam pirivu','51','35','641606','BG2025006','2023-11-18',28,23,7,'Bachelor\'s Degree','Bsc. Physics',10000.00,23,'','','579095383597','Arumugam',1,'Active','2025-01-15 06:41:18','2025-01-20 12:48:45',22,22,'Business','9999999999','Deceased','Kalamani','Housewife','8220850411','Living','5, R.V.E nagar 8th street, Rakkiyapalayam pirivu, tirupur - 641606'),(91,'Yasmin banu','D ','2000-07-21',5,'1736923847349-yasmin.png','<EMAIL>','6380915093','Irfan','6380331008','306/284 college road, Sathick bacha nagar opposite','51','35','641602','BG2025007','2022-12-12',28,22,7,'Bachelor\'s Degree','BA. ENGLISH',12000.00,21,'','','890660811990','Devan mydeen',1,'Active','2025-01-15 06:50:47','2025-01-16 11:24:52',22,22,'pppppppp','9942644394','Living','ppppppp','ppppp','9999999999','Living',NULL),(92,'Mythili','S ','1993-04-30',6,'1736924275491-mythili.png','<EMAIL>','8220850411','Sankaran','9578930075','5, R.V.E Nagar, Rakkiyapalayam pirivu','51','35','641606','BG2025008','2023-11-01',28,23,7,'Bachelor\'s Degree','B.Pharm',18000.00,23,'','','267972058564','Arumugam',1,'Active','2025-01-15 06:57:55','2025-01-20 12:47:12',22,22,'Business','9999999999','Deceased','Kalamani','Housewife','9790356757','Living','5, R.V.E Nagar, Rakkiyapalayam pirivu, tirupur - 641606'),(93,'Karpagam','D ','2001-06-03',6,'1736924932313-WhatsApp Image 2024-07-06 at 5.05.05 PM.jpeg','<EMAIL>','6374803894','Dharmaraj','9976807864','Makhaliyamman kovil backside, veerapandi main road','51','35','641605','BG2025009','2024-07-04',28,23,7,'Bachelor\'s Degree','Bsc. Chemistry',10000.00,21,'','','343473896587','Dharmaraj',1,'Active','2025-01-15 07:08:52','2025-01-16 11:25:02',22,22,'Tailor','9999999999','Living','Sangareswari','Tailor','9999999999','Living','Makhaliyamman kovil backside, veerapandi main road, Tirupur - 641605'),(95,'Rajalakshmi','S ','1986-06-29',6,'1736930489259-WhatsApp Image 2024-02-22 at 2.31.10 PM (1).jpeg','<EMAIL>','9600637415','Indiran','8438584415','1/217 Thoravalur merkupathi (PO) Kunnathur','51','35','638103','BG2025010','2024-02-01',28,23,7,'High School','12th',18000.00,23,'','','892164474864','kkkkkkkkkkkk',1,'Active','2025-01-15 08:41:29','2025-01-16 11:25:19',22,22,'qqqqqqq','9999999999','Living','Annakodi','House wife','9999999999','Living','1/217 Thoravalur merkupathi (PO) Kunnathur, Tirupur-638103'),(96,'Kiruthika','P ','2001-05-14',6,'1737024483696-WhatsApp Image 2024-03-08 at 6.28.43 PM.jpeg','<EMAIL>','6381957655','Palanisamy','9788291574','69, Arugampalayam, s. kathanganni post, Anaipalayam, Uthukuli RS','51','35','638752','BG2025011','2024-05-14',28,23,7,'Diploma','D. Pharm',11000.00,21,'','','934418009866','Palanisamy',1,'Active','2025-01-16 10:48:03','2025-01-16 11:25:29',22,22,'Finance','9788291574','Living','Pavalakodi','Housewife','9999999999','Living','69, Arugampalayam, s. kathanganni post, Anaipalayam, Uthukuli RS, Tirupur - 638752'),(97,'Azarudeen','A ','1989-09-01',5,'1737025601948-WhatsApp Image 2024-01-08 at 6.08.01 PM.jpeg','<EMAIL>','9025515436','Abdul majid','8056300554','62-51, Meera bawa street','53','35','639207','BG2025012','2010-02-17',28,22,7,'High School','8th',14000.00,21,'7','12','434892190315','Abdul majid',1,'Active','2025-01-16 11:06:42','2025-01-16 11:06:51',22,22,'Cooking master','8056300554','Living','Ramjan Begam','House wife','9629493969','Living','62-51, Meera bawa street, Pallapatti, Karur - 639207'),(98,' Syed mohammed Moideen','A ','1987-08-05',5,'1737040883674-WhatsApp Image 2024-01-08 at 6.08.05 PM.jpeg','<EMAIL>','9952814302','Thahira Thasneem','9566887570','2, Chairman street, Dharapuram','51','35','638656','BG2025013','2019-12-20',26,24,7,'High School','12th',14000.00,21,'','','276523183722','Amjahasan',1,'Active','2025-01-16 15:21:23','2025-01-16 15:21:38',22,22,'Business','8925243284','Living','Rastha begam','House wife','9999999999','Living','2, Chairman street, Dharapuram- 638656'),(99,'Dharani Devi','T ','2004-07-07',6,'1737289489214-WhatsApp Image 2025-01-19 at 5.54.07 PM.jpeg','<EMAIL>','9384268126','Pandiselvi','7639096086','113/273 3rd street, kannan street opposite, perumanallur','51','35','641666','BG2025014','2024-09-01',28,23,7,'Diploma','D.Pharma',11000.00,21,'6','12','924885734901','Tirupathi',1,'Active','2025-01-19 12:24:49','2025-01-19 12:24:58',22,22,'Not alive','7639096086','Deceased','Pandiselvi','Business','7639096086','Living','113/273 3rd street, kannan street opposite, perumanallur, Tiruppur - 641666'),(100,'Sharmidha','D ','2005-03-18',6,'1737291341718-WhatsApp Image 2024-11-27 at 2.27.48 PM.jpeg','<EMAIL>','8695667578','Dakshinamurthy','9344213290','126/6 Surya krishna nagar, Murugampalayam road','51','35','641687','BG2025015','2024-11-21',28,23,8,'Diploma','D.Pharma',5000.00,21,'6','12','288601606310','Dakshinamoorthy',1,'Active','2025-01-19 12:55:41','2025-01-19 12:55:45',22,22,'Business','9344213290','Living','Rajalakshmi','Housewife','9999999999','Living','126/6 Surya krishna nagar, Murugampalayam road, Tirupur - 641687'),(101,'Shanmuga priya','S ','2003-08-26',6,'1737292024193-WhatsApp Image 2024-08-09 at 5.17.28 PM.jpeg','<EMAIL>','6369313160','Sakthivel','9791868768','L.R.G Layout 1st street, Rayapuram main road','51','35','641687','BG2025016','2024-08-09',28,23,8,'Bachelor\'s Degree','Bsc',6000.00,21,'6','12','915628972747','Sakthivel',1,'Active','2025-01-19 13:07:04','2025-01-19 13:07:07',22,22,'Business','9791868768','Living','Nagalakshmi','Housewife','9999999999','Living','L.R.G Layout 1st street, Rayapuram main road, Tirupur - 641687'),(102,'Yogashree','S ','2003-07-12',6,'1737292636321-WhatsApp Image 2024-09-19 at 11.08.08 AM.jpeg','<EMAIL>','6384056573','Selvam','9999999999','4/357, Cotton mill road, 1st street, near new bus stand','51','35','641602','BG2025017','2024-09-16',28,23,7,'Bachelor\'s Degree','Bsc. Microbiology',10000.00,23,'6','12','699057945837','Selvam',1,'Active','2025-01-19 13:17:16','2025-01-19 13:17:19',22,22,'Daily wages','9999999999','Living','BAGYALAKSHMI','Housewife','9843674505','Living','4/357, Cotton mill road, 1st street, near new bus stand, Tirupur - 641602'),(103,'Gopal raj','G ','1986-01-13',5,'1737293858539-WhatsApp Image 2024-12-10 at 11.23.40 AM.jpeg','<EMAIL>','9750805331','Suganya','6381252754','Dharapuram, Alangiya road, ','51','35','638656','BG2025018','2024-12-23',28,22,7,'Diploma','D.Pharma',20000.00,21,'6','12','842087594052','G ',1,'Active','2025-01-19 13:37:38','2025-01-19 13:37:41',22,22,'No more','9999999999','Deceased','Kattiyammal','Housewife','9524653395','Living','Dharapuram, Alangiya road, Dharapuram - 638656'),(104,'Muhamed Sameel Akthar ','MR','2005-03-16',5,'1737294360260-WhatsApp Image 2024-01-08 at 6.07.31 PM.jpeg','<EMAIL>','6380990905','Rabiya banu','8675355352','120, Chellumeeran nagar','53','35','639207','BG2025019','2024-06-22',28,22,7,'Bachelor\'s Degree','Bsc. CS- Discontinued',12000.00,21,'7','12','319563660795','Riyazdeen',1,'Active','2025-01-19 13:46:00','2025-01-19 13:46:03',22,22,'Business','9999999999','Living','Rabiya banu','House wife','8675355352','Living','120, Chellumeeran nagar, Pallapatti - 639207'),(105,'Ragupathi Raja','Ramasamy','2003-01-05',5,'1737294800083-RAGUPATHI.jpeg','<EMAIL>','7812827698','q ','1111111111','1/1 Velusamy compound, Sivasakthi nagar 1st street, Parapalayam','51','35','641604','BG2025020','2024-02-07',28,22,8,'Bachelor\'s Degree','B.Pharm',6000.00,21,'6','12','371432474678','Ramasamy',1,'Active','2025-01-19 13:53:20','2025-01-19 13:53:22',22,22,'No more','1111111111','Deceased','q ','Tailor','1111111111','Living','1/1 Velusamy compound, Sivasakthi nagar 1st street, Parapalayam, Tirupur - 641604'),(106,'Irfana Fazila','J ','2005-07-02',6,'1737295159428-WhatsApp Image 2024-12-03 at 10.41.55 AM.jpeg','<EMAIL>','8778655198','Jabsarulla','8778956650','Sathikbasha nagar, College road, odakkadu','51','35','641602','BG2025021','2024-12-03',28,23,7,'High School','11th',10000.00,21,'7','12','869719426185','Jabarulla',1,'Active','2025-01-19 13:59:19','2025-01-19 13:59:21',22,22,'Biryani master','8778956650','Living','Recky bivi','House wife','9999999999','Living','Sathikbasha nagar, College road, odakkadu, Tiruppur - 641602'),(107,'Tippu sultan','B ','1993-10-01',5,'1737295655525-WhatsApp Image 2024-06-29 at 11.54.00 AM.jpeg','<EMAIL>','8220464379','Nowfiya banu','8056391975','11/197, Thiyagi kumaran colony, Anna nagar','51','35','641602','BG2025022','2022-12-10',28,22,7,'High School','12th',15000.00,23,'7','12','442299844270','BheerBasha',1,'Active','2025-01-19 14:07:35','2025-01-19 14:07:37',22,22,'Banian company labour','9999999999','Living','Ramijaa','Housewife','9999999999','Living','11/197, Thiyagi kumaran colony, Anna nagar- Tiruppur - 641602'),(108,'Kadher Maideen','A ','2015-08-29',5,'1737297001488-WhatsApp Image 2024-01-08 at 6.07.23 PM.jpeg','<EMAIL>','8883852483','Aarifa','8148505665','3/119 Door no. 3/95 Thottanampatti west street, Nallamanarkottai','54','35','624005','BG2025023','2003-10-26',28,24,7,'High School','9th',20000.00,22,'7','12','550175537308','Ahammed Ibrahim',1,'Active','2025-01-19 14:30:01','2025-01-19 14:30:03',22,22,'No  More','9999999999','Living','Noorjahan','Housewife','9994903404','Living','3/119 Door no. 3/95 Thottanampatti west street, Nallamanarkottai, Dindigul - 624005'),(109,'Madhu ','Vimal kumar pandey','2003-06-15',6,'1737298066548-WhatsApp Image 2024-11-22 at 9.02.31 PM.jpeg','<EMAIL>','8148414033','Vimal Pandey','6380524546','25, Kg layout, Behind dhanalakshmi theatre, Kongu main road','51','35','641607','BG2025024','2024-12-21',28,23,8,'Diploma','D.Pharm',6000.00,21,'6','12','233048644111','Vimal pandey',1,'Active','2025-01-19 14:47:46','2025-01-19 14:47:50',22,22,'Business','8148414033','Living','Anita Pandey','Housewife','9999999999','Living','25, Kg layout, Behind dhanalakshmi theatre, Kongu main road, Tirupur - 641607'),(110,'Haamitha begam','S ','2004-06-21',5,'1737298884890-WhatsApp Image 2025-01-07 at 5.15.11 PM.jpeg','<EMAIL>','8925112778','Sheik Barithu','7448327778','1/801, thotathupalayam, Neriperichal','51','35','641602','BG2025025','2024-12-16',28,23,7,'Bachelor\'s Degree','B.Com ( CA)',10000.00,21,'7','12','235822366280','Sheik Barithu',1,'Active','2025-01-19 15:01:25','2025-01-19 15:01:26',22,22,'Tailor','7448327778','Living','Ramija Banu','House wife','9999999999','Living','1/801, thotathupalayam, Neriperichal, Tirupur - 641602'),(111,'Anandha jothi','S ','2003-04-05',6,'1737299492678-WhatsApp Image 2024-09-25 at 12.17.27 PM.jpeg','<EMAIL>','6385799331','Thangapandian','9500265624','Kalikumaran nagar, veerapandi','51','35','641604','BG2025026','2024-09-23',28,25,7,'Bachelor\'s Degree','Bsc. CS',10000.00,21,'','','534063674674','Saravanapandiyan',1,'Active','2025-01-19 15:11:32','2025-01-20 12:48:58',22,22,'Nursery','9500265624','Living','Jayalakshmi','Housewife','9999999999','Living','Kalikumaran nagar, veerapandi, Tirupur -641604'),(112,'Navaneethan','R ','2006-12-18',5,'1737299851868-WhatsApp Image 2024-07-11 at 6.56.49 PM (1).jpeg','<EMAIL>','9597297492','Ramalingam','7094624541','Kallampalayam, Pilliyar kovil street','51','35','641601','BG2025027','2024-07-11',27,22,8,'Bachelor\'s Degree','Bsc. CS',6000.00,21,'6','12','570451342596','Ramalingam',1,'Active','2025-01-19 15:17:32','2025-01-19 15:17:34',22,22,'Tailor','7094624541','Living','Sathya','Tailor','9999999999','Living','Kallampalayam, Pilliyar kovil street, Tirupur - 641601'),(113,'Beer mohammed','   ','1972-06-25',5,NULL,'<EMAIL>','8015241387','Yasmi','9043409510','263/B31/381, Rahumaniyapuram, 5th street, Krishnapuram, kadayanallur','54','35','627751','BG2025028','2024-01-21',28,23,7,'High School','8th',14000.00,23,'7','12','526569740343','Diwan mydeen',1,'Active','2025-01-19 15:32:09','2025-01-19 15:32:12',22,22,'v ','9999999999','Living','v ','v ','9999999999','Living','263/B31/381, Rahumaniyapuram, 5th street, Krishnapuram, kadayanallur - 627751'),(114,'Mohammed Nasik ali','M ','2003-05-30',5,'1737301297998-WhatsApp Image 2024-02-26 at 1.44.32 PM (1).jpeg','<EMAIL>','8946062561','Mohammed ibrahim','9999999999','4/5 A, pallivasal street, Komaralingam,','51','35','642113','BG2025029','2023-05-06',28,22,7,'Diploma','DMLT',12500.00,21,'7','12','941711185409','MOHAMED IBRAHIM',1,'Active','2025-01-19 15:41:38','2025-01-19 15:41:40',22,22,'M ','9999999999','Living','M ','V ','8888888888','Living','4/5 A, pallivasal street, Komaralingam, Udumalpet'),(115,'Dhanu sri','S ','2005-07-16',6,'1737363939369-WhatsApp Image 2025-01-17 at 3.54.23 PM.jpeg','<EMAIL>','8754138728','Senthil kumar','7339438728','2/67 A, Anaipalayam ( PO) Uthukuli R.S','51','35','638752','BG2025030','2025-01-17',28,23,8,'Bachelor\'s Degree','Bsc. Microbiology',10000.00,21,'6','12','383826417917','Senthilkumar',1,'Active','2025-01-20 09:05:39','2025-01-20 09:05:43',22,22,'Daily wages','7339438728','Living','Kalamani','Daily wages','7810097238','Living','2/67 A, Anaipalayam ( PO) Uthukuli R.S, Tirupur - 638752'),(116,'Sudhakar','K ','1981-06-08',5,'1737364508258-WhatsApp Image 2025-01-20 at 1.24.15 PM.jpeg','<EMAIL>','9345135514','Kumar','9894247428','9/287 Neelakadu street, Rakkiapalayam road, ammapalayam','51','35','641604','BG2025031','2025-01-20',27,23,7,'Diploma','Production engineering',15000.00,23,'6','12','760409166041','R. Kumar',1,'Active','2025-01-20 09:15:09','2025-01-20 09:15:11',22,22,'Farmer','9894247428','Living','Rani','House wife','9999999999','Living','5/49 - 35 d Police station road, mallur ( P.O) Salem district - 636203'),(117,'Santhosh','P s','2003-04-05',5,'1737364936595-WhatsApp Image 2024-07-17 at 5.53.00 PM.jpeg','<EMAIL>','9524275728','Periyakaruppan','9965774486','7, therkku thotam 1st street, karuvampalayam','51','35','641604','BG2025032','2025-07-16',28,22,8,'Other','Pharm.D',6000.00,21,'','','347435665371','Periyakaruppan',1,'Active','2025-01-20 09:22:16','2025-01-20 17:41:50',22,6,'Business','9965774486','Living','Pandiselvi','Housewife','9999999999','Living','7, therkku thotam 1st street, karuvampalayam, Tiruppur - 641604');
/*!40000 ALTER TABLE `staff` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `staff_store_mapping`
--

DROP TABLE IF EXISTS `staff_store_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_store_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `store_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_staff_store` (`staff_id`,`store_id`),
  KEY `store_id` (`store_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `staff_store_mapping_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`),
  CONSTRAINT `staff_store_mapping_ibfk_2` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`),
  CONSTRAINT `staff_store_mapping_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `staff_store_mapping_ibfk_4` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `staff_store_mapping`
--

LOCK TABLES `staff_store_mapping` WRITE;
/*!40000 ALTER TABLE `staff_store_mapping` DISABLE KEYS */;
INSERT INTO `staff_store_mapping` VALUES (17,95,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(18,93,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(19,92,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(20,91,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(22,89,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(24,87,15,1,'2025-01-15 10:31:52','2025-01-15 10:31:52',6,6),(26,117,15,1,'2025-01-22 16:08:39','2025-01-22 16:08:39',6,6),(27,86,15,1,'2025-01-22 16:09:20','2025-01-22 16:09:20',6,6),(55,104,15,1,'2025-01-23 15:42:07','2025-01-23 15:42:07',6,6),(56,112,15,1,'2025-01-23 15:42:07','2025-01-23 15:42:07',6,6),(57,105,15,1,'2025-01-23 15:42:07','2025-01-23 15:42:07',6,6),(58,115,15,1,'2025-01-23 16:11:50','2025-01-23 16:11:50',6,6),(59,113,15,1,'2025-01-23 16:11:50','2025-01-23 16:11:50',6,6);
/*!40000 ALTER TABLE `staff_store_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `states`
--

DROP TABLE IF EXISTS `states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `states` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `states_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `states`
--

LOCK TABLES `states` WRITE;
/*!40000 ALTER TABLE `states` DISABLE KEYS */;
INSERT INTO `states` VALUES (35,'Tamil Nadu','2025-01-08 12:51:51','2025-01-09 04:39:35',22);
/*!40000 ALTER TABLE `states` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stores`
--

DROP TABLE IF EXISTS `stores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stores` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `city_id` int NOT NULL,
  `state_id` int NOT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `opening_hours` time DEFAULT NULL,
  `closing_hours` time DEFAULT NULL,
  `manager_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `city_id` (`city_id`),
  KEY `state_id` (`state_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `fk_store_manager` (`manager_id`),
  CONSTRAINT `fk_store_manager` FOREIGN KEY (`manager_id`) REFERENCES `staff` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `stores_ibfk_1` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`),
  CONSTRAINT `stores_ibfk_2` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`),
  CONSTRAINT `stores_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `stores_ibfk_5` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stores`
--

LOCK TABLES `stores` WRITE;
/*!40000 ALTER TABLE `stores` DISABLE KEYS */;
INSERT INTO `stores` VALUES (15,'Bawa medical mart','64/66 Municipal office road, Tirupur - 641604',51,35,'641604','**********','<EMAIL>','08:00:00','23:00:00',54,1,'2025-01-13 14:58:26','2025-01-22 14:58:23',22,6);
/*!40000 ALTER TABLE `stores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int NOT NULL,
  `mobile_number` varchar(15) NOT NULL,
  `email` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `mobile_number` (`mobile_number`),
  UNIQUE KEY `email` (`email`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (6,'testuser','$2b$10$tVN/Rr/Ecun2kmKfEp1ycOov8IYv94ADEloDsHalUTw50rIlqPOgG',1,'9898998981','<EMAIL>','2024-08-06 13:30:56',1,'2024-12-21 13:47:35',NULL,6),(21,'Manikandan','$2b$10$i6ImHXR1n.MjZ97nrK54Ve1mbSWawAiJ3F.YEG6Tz7snAm3Ij4vqq',2,'9819829812','<EMAIL>','2024-12-25 08:38:13',1,'2024-12-25 08:38:13',6,NULL),(22,'Prem','$2b$10$NEazr/tIxCo5jRIcmWaFge/.WTNbYDCjnp4P7X/x8fjbwJAB5TxmW',2,'8248967976','<EMAIL>','2024-12-25 08:45:23',1,'2024-12-25 08:45:23',6,NULL),(23,'Shameer','$2b$10$TE4J6goPquteYlp3Pg7ceewrGPLXOikn5arxL7x5WMgCosjJ/sCTm',1,'9566555533','<EMAIL>','2025-01-13 04:40:12',1,'2025-01-20 09:46:04',6,6);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-03-06 22:43:31
