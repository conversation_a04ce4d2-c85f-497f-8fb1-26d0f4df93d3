require('dotenv').config();
const express = require('express');
const bodyParser = require('body-parser');
const session = require('express-session');
const mysql2 = require('mysql2/promise');  // Add this line
const MySQLStore = require('express-mysql-session')(session);
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const moduleRoutes = require('./routes/moduleRoutes');
const masterRoutes = require('./routes/masterRoutes');
const staffRoutes = require('./routes/staffRoutes');
const attendanceRoutes = require('./routes/attendanceRoutes');
const interviewRoutes = require('./routes/interviewRoutes');
const publicRoutes = require('./routes/publicRoutes');


const cors = require('cors');
const { checkSession } = require('./middlewares/authMiddleware');

// Initialize the models to ensure tables are created
const User = require('./models/userModel');
const Role = require('./models/roleModel');
const Module = require('./models/moduleModel');
const Master = require('./models/masterModel');



const app = express();
const port = process.env.PORT || 3000;

const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
};

// Create the database connection pool
const pool = mysql2.createPool(dbConfig);

// Create the session store
const sessionStore = new MySQLStore({}, pool);

// Enable CORS for all routes
app.use(cors({
    origin: 'http://localhost:4200',
    // origin: 'http://************',

    credentials: true
}));

app.use(bodyParser.json());

app.use(session({
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    store: sessionStore,
    cookie: {
        maxAge: 1000 * 60 * 60, // 1 hour
        httpOnly: false,
        secure: false, // Set to true if using HTTPS
        sameSite: 'lax'
    }
}));

app.get('/', checkSession, (req, res) => {
    if (req.session.userRole === 'superadmin') {
        res.redirect('/superadmin/superdash');
    } else {
        res.redirect('/login');
    }
});

app.use('/uploads', express.static('uploads'));
app.use('/auth', authRoutes);
app.use('/module', moduleRoutes);
app.use('/master', masterRoutes);
app.use('/staff', staffRoutes);
app.use('/attendance', attendanceRoutes);
app.use('/user', userRoutes);
app.use('/interview', interviewRoutes);
app.use('/public', publicRoutes);


// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ success: false, message: 'Internal Server Error' });
});

app.listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`);
});