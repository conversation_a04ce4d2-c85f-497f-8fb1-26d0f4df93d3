const Staff = require('../models/staffModel');
const path = require('path');
const fs = require('fs');

const deleteFile = (filePath) => {
    fs.unlink(filePath, (err) => {
        if (err) console.error('Error deleting file:', err);
    });
};

const staffController = {
        addStaff: async (req, res) => {
            try {
                console.log('Starting staff creation...');
                
                // 1. Extract and validate basic data
                const staffData = {
                    // Personal Information
                    firstName: req.body.firstName,
                    lastName: req.body.lastName,
                    dateOfBirth: req.body.dateOfBirth ? new Date(req.body.dateOfBirth).toISOString().split('T')[0] : null,
                    genderId: parseInt(req.body.genderId),
                    email: req.body.email,
                    phoneNumber: req.body.phoneNumber,
                    emergencyContactName: req.body.emergencyContactName,
                    emergencyContactNumber: req.body.emergencyContactNumber,
                    
                    // Address Information
                    address: req.body.address,
                    city: req.body.city,
                    state: req.body.state,
                    postalCode: req.body.postalCode,
                    
                    // Employment Information
                    employeeId: req.body.employeeId,
                    hireDate: req.body.hireDate ? new Date(req.body.hireDate).toISOString().split('T')[0] : null,
                    departmentId: parseInt(req.body.departmentId),
                    designationId: parseInt(req.body.designationId),
                    employmentTypeId: parseInt(req.body.employmentTypeId),
                    
                    // Education and Professional Information
                    educationLevel: req.body.educationLevel,
                    degrees: req.body.degrees,
                    salary: parseFloat(req.body.salary),
                    bloodGroupId: parseInt(req.body.bloodGroupId),
                    religion: req.body.religion || null,
                    community: req.body.community || null,
                    idProof: req.body.idProof,
                    
                    // Family Information
                    father_name: req.body.father_name || null,
                    father_occupation: req.body.father_occupation || null,
                    father_contact: req.body.father_contact || null,
                    father_status: req.body.father_status || 'Living',
                    mother_name: req.body.mother_name || null,
                    mother_occupation: req.body.mother_occupation || null,
                    mother_contact: req.body.mother_contact || null,
                    mother_status: req.body.mother_status || 'Living',
                    family_address: req.body.family_address || null,
                    
                    // Status Information
                    isActive: req.body.isActive === true ? 1 : 0,
                    employmentStatus: req.body.employmentStatus || 'Active',
                    
                    // Metadata
                    created_by: req.session.userId
                };
    
                // 2. Profile Picture Handling
                if (req.file) {
                    staffData.profilePicture = req.file.filename;
                }
    
                // 3. Create Staff Record
                Staff.createStaff(staffData, (err, staffResult) => {
                    if (err) {
                        console.error('Database Error:', err);
                        return res.status(500).json({
                            success: false,
                            message: 'Error creating staff record',
                            error: err.message
                        });
                    }
    
                    const staffId = staffResult.insertId;
    
                    // 4. Handle Siblings (if any)
                    if (req.body.siblings) {
                        let siblings;
                        try {
                            siblings = typeof req.body.siblings === 'string' 
                                ? JSON.parse(req.body.siblings) 
                                : req.body.siblings;
                        } catch (e) {
                            console.error('Error parsing siblings:', e);
                            siblings = [];
                        }
    
                        if (Array.isArray(siblings) && siblings.length > 0) {
                            const siblingPromises = siblings.map(sibling => {
                                return new Promise((resolve, reject) => {
                                    const siblingData = {
                                        staff_id: staffId,
                                        name: sibling.name,
                                        date_of_birth: sibling.date_of_birth ? new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                        gender: sibling.gender,
                                        occupation: sibling.occupation,
                                        marital_status: sibling.marital_status,
                                        contact: sibling.contact,
                                        status: sibling.status || 'Living',
                                        is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                        created_by: req.session.userId
                                    };
    
                                    Staff.createSibling(siblingData, (err, result) => {
                                        if (err) reject(err);
                                        else resolve(result);
                                    });
                                });
                            });
    
                            Promise.all(siblingPromises)
                                .then(() => {
                                    res.status(201).json({
                                        success: true,
                                        message: 'Staff and siblings created successfully',
                                        staffId: staffId
                                    });
                                })
                                .catch(error => {
                                    console.error('Error creating siblings:', error);
                                    res.status(201).json({
                                        success: true,
                                        message: 'Staff created but error with siblings',
                                        staffId: staffId,
                                        siblingError: error.message
                                    });
                                });
                        } else {
                            res.status(201).json({
                                success: true,
                                message: 'Staff created successfully (no siblings)',
                                staffId: staffId
                            });
                        }
                    } else {
                        res.status(201).json({
                            success: true,
                            message: 'Staff created successfully',
                            staffId: staffId
                        });
                    }
                });
    
            } catch (error) {
                console.error('Controller Error:', error);
                res.status(500).json({
                    success: false,
                    message: 'Error processing staff creation',
                    error: error.message
                });
            }
        },

    getAllStaff: (req, res) => {
        Staff.getAllStaff((err, results) => {
            if (err) {
                res.status(500).json({
                    success: false,
                    message: 'Error fetching staff',
                    error: err
                });
            } else {
                res.status(200).json({
                    success: true,
                    staff: results
                });
            }
        });
    },

    getStaffById: (req, res) => {
        const { id } = req.params;
        
        Staff.getStaffById(id, (err, staffResult) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching staff',
                    error: err
                });
            }

            if (!staffResult || staffResult.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Staff not found'
                });
            }

            // Get siblings data
            Staff.getAllSiblings(id, (siblingErr, siblings) => {
                if (siblingErr) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error fetching siblings',
                        error: siblingErr
                    });
                }

                // Combine staff and siblings data
                const staffData = {
                    ...staffResult[0],
                    siblings: siblings || []
                };

                res.status(200).json({
                    success: true,
                    staff: staffData
                });
            });
        });
    },

    // In staffController.js
    updateStaff: (req, res) => {
        try {
            const { id } = req.params;
            const staffData = { ...req.body };
            let siblings;
    
            // Parse siblings if present
            if (req.body.siblings) {
                siblings = typeof req.body.siblings === 'string' 
                    ? JSON.parse(req.body.siblings) 
                    : req.body.siblings;
            }
    
            // Handle profile picture if uploaded
            if (req.file) {
                staffData.profilePicture = req.file.filename;
            }
    
            // Get existing staff data to handle profile picture
            Staff.getStaffById(id, (err, existingStaff) => {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error fetching staff details',
                        error: err
                    });
                }
    
                // If new profile picture, delete the old one
                if (req.file && existingStaff[0].profilePicture) {
                    const oldPicturePath = path.join(__dirname, '../uploads', existingStaff[0].profilePicture);
                    fs.unlink(oldPicturePath, (unlinkErr) => {
                        if (unlinkErr) {
                            console.error('Error deleting old profile picture:', unlinkErr);
                        }
                    });
                }
    
                staffData.updated_by = req.session.userId;
    
                // Update main staff record
                Staff.updateStaff(id, staffData, (updateErr, result) => {
                    if (updateErr) {
                        console.error('Error updating staff:', updateErr);
                        return res.status(500).json({
                            success: false,
                            message: 'Error updating staff',
                            error: updateErr
                        });
                    }
    
                    // If no siblings to update, return success
                    if (!siblings || !Array.isArray(siblings)) {
                        return res.json({
                            success: true,
                            message: 'Staff updated successfully'
                        });
                    }
    
                    // Handle siblings updates
                    const siblingPromises = siblings.map(sibling => {
                        return new Promise((resolve, reject) => {
                            if (sibling.id) {
                                // Update existing sibling
                                const siblingData = {
                                    name: sibling.name,
                                    date_of_birth: sibling.date_of_birth ? 
                                        new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                    gender: sibling.gender,
                                    occupation: sibling.occupation || null,
                                    marital_status: sibling.marital_status || 'Single',
                                    contact: sibling.contact || null,
                                    status: sibling.status || 'Living',
                                    is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                    updated_by: req.session.userId
                                };
                                Staff.updateSibling(sibling.id, siblingData, (err, result) => {
                                    if (err) reject(err);
                                    else resolve(result);
                                });
                            } else {
                                // Create new sibling
                                const siblingData = {
                                    staff_id: id,
                                    name: sibling.name,
                                    date_of_birth: sibling.date_of_birth ? 
                                        new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                    gender: sibling.gender,
                                    occupation: sibling.occupation || null,
                                    marital_status: sibling.marital_status || 'Single',
                                    contact: sibling.contact || null,
                                    status: sibling.status || 'Living',
                                    is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                    created_by: req.session.userId
                                };
                                Staff.createSibling(siblingData, (err, result) => {
                                    if (err) reject(err);
                                    else resolve(result);
                                });
                            }
                        });
                    });
    
                    Promise.all(siblingPromises)
                        .then(() => {
                            res.json({
                                success: true,
                                message: 'Staff and siblings updated successfully'
                            });
                        })
                        .catch(error => {
                            console.error('Error updating siblings:', error);
                            res.json({
                                success: true,
                                message: 'Staff updated but error with siblings',
                                siblingError: error.message
                            });
                        });
                });
            });
    
        } catch (error) {
            console.error('Controller Error:', error);
            res.status(500).json({
                success: false,
                message: 'Error processing staff update',
                error: error.message
            });
        }
    },

    deleteStaff: (req, res) => {
        const { id } = req.params;
        Staff.deleteStaff(id, (err, result) => {
            if (err) {
                res.status(500).json({
                    success: false,
                    message: 'Error deleting staff',
                    error: err
                });
            } else if (result.affectedRows === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Staff not found'
                });
            } else {
                res.status(200).json({
                    success: true,
                    message: 'Staff deleted successfully'
                });
            }
        });
    },

    deleteStaffs: (req, res) => {
        const { staffIds } = req.body;
        if (!Array.isArray(staffIds) || staffIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid staff IDs'
            });
        }

        Staff.deleteStaffs(staffIds, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting staff members',
                    error: err
                });
            }
            return res.json({
                success: true,
                message: 'Staff members deleted successfully',
                affectedRows: result.affectedRows
            });
        });
    },

    toggleStaffStatus: (req, res) => {
        const { id } = req.params;
        const updated_by = req.session.userId;
        Staff.toggleStaffStatus(id, updated_by, (err, result) => {
            if (err) {
                res.status(500).json({
                    success: false,
                    message: 'Error toggling staff status',
                    error: err
                });
            } else if (result.affectedRows === 0) {
                res.status(404).json({
                    success: false,
                    message: 'Staff not found'
                });
            } else {
                res.status(200).json({
                    success: true,
                    message: 'Staff status toggled successfully'
                });
            }
        });
    },

    updateProfilePicture: (req, res) => {
        const { id } = req.params;
        const updatedBy = req.session.userId;

        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: 'No file uploaded'
            });
        }

        Staff.getStaffById(id, (err, staff) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching staff',
                    error: err
                });
            }
            if (staff.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Staff not found'
                });
            }

            // Delete old profile picture if it exists
            if (staff[0].profilePicture) {
                const oldPicturePath = path.join(__dirname, '../uploads', staff[0].profilePicture);
                fs.unlink(oldPicturePath, (err) => {
                    if (err) console.error('Error deleting old profile picture:', err);
                });
            }

            // Update with new profile picture
            Staff.updateProfilePicture(id, req.file.filename, updatedBy, (updateErr, updateResult) => {
                if (updateErr) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating profile picture',
                        error: updateErr
                    });
                }
                res.json({
                    success: true,
                    message: 'Profile picture updated successfully'
                });
            });
        });
    },

    // Sibling management methods
    addSibling: (req, res) => {
        const siblingData = {
            ...req.body,
            staff_id: req.params.staffId,
            created_by: req.session.userId
        };

        if (siblingData.date_of_birth) {
            siblingData.date_of_birth = new Date(siblingData.date_of_birth).toISOString().split('T')[0];
        }

        Staff.createSibling(siblingData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error adding sibling',
                    error: err
                });
            }

            res.status(201).json({
                success: true,
                message: 'Sibling added successfully',
                data: result
            });
        });
    },

    updateSibling: (req, res) => {
        const { id } = req.params;
        const siblingData = {
            ...req.body,
            updated_by: req.session.userId
        };

        if (siblingData.date_of_birth) {
            siblingData.date_of_birth = new Date(siblingData.date_of_birth).toISOString().split('T')[0];
        }

        Staff.updateSibling(id, siblingData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error updating sibling',
                    error: err
                });
            }

            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Sibling not found'
                });
            }

            res.json({
                success: true,
                message: 'Sibling updated successfully'
            });
        });
    },

    deleteSibling: (req, res) => {
        const { id } = req.params;

        Staff.deleteSibling(id, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting sibling',
                    error: err
                });
            }

            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Sibling not found'
                });
            }

            res.json({
                success: true,
                message: 'Sibling deleted successfully'
            });
        });
    }
};

module.exports = staffController;