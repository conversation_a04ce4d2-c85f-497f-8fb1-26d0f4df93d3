const Master = require('../models/masterModel');

const addState = (req, res) => {
    const { name } = req.body;
    const created_by = req.session.userId;  // Assuming userId is stored in the session
    Master.createState(name, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding state', error: err });
        } else {
            res.status(201).json({ success: true, message: 'State added successfully' });
        }
    });
};

const getAllStates = (req, res) => {
    Master.getAllStates((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching states', error: err });
        } else {
            console.log(results);
            res.status(200).json({ success: true, states: results });
        }
    });
};



 
const getStateById = (req, res) => {
    const { id } = req.params;
    Master.getStateById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching state', error: err });
        } else {
            res.status(200).json({ success: true, state: result });
        }
    });
};

const updateState = (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    Master.updateState(id, name, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating state', error: err });
        } else {
            res.status(200).json({ success: true, message: 'State updated successfully' });
        }
    });
};

const deleteState = (req, res) => {
    const { id } = req.params;
    Master.deleteState(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting state', error: err });
        } else {
            res.status(200).json({ success: true, message: 'State deleted successfully' });
        }
    });
};

const addCity = (req, res) => {
    const { state_id, name } = req.body;
    const created_by = req.session.userId;  // Assuming userId is stored in the session
    Master.createCity(state_id, name, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding city', error: err });
        } else {
            res.status(201).json({ success: true, message: 'City added successfully' });
        }
    });
};

const getAllCities = (req, res) => {
    Master.getAllCities((err, results) => {
        if (err) {

            res.status(500).json({ success: false, message: 'Error fetching cities', error: err });
        } else {
            res.status(200).json({ success: true, cities: results });
        }
    });
};

const getCityById = (req, res) => {
    const { id } = req.params;
    Master.getCityById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching city', error: err });
        } else if (result.length === 0) {
            res.status(404).json({ success: false, message: 'City not found' });
        } else {
            res.status(200).json({ success: true, city: result[0] });
        }
    });
};

const updateCity = (req, res) => {
    const { id } = req.params;
    const { state_id, name } = req.body;
    const updated_by = req.session.userId;
    Master.updateCity(id, state_id, name, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating city', error: err });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ success: false, message: 'City not found' });
        } else {
            res.status(200).json({ success: true, message: 'City updated successfully' });
        }
    });
};

const deleteCity = (req, res) => {
    const { id } = req.params;
    Master.deleteCity(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting city', error: err });
        } else if (result.affectedRows === 0) {
            res.status(404).json({ success: false, message: 'City not found' });
        } else {
            res.status(200).json({ success: true, message: 'City deleted successfully' });
        }
    });
};

const deleteCities = (req, res) => {
    const { cityIds } = req.body;
    if (!Array.isArray(cityIds) || cityIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid city IDs' });
    }

    Master.deleteCities(cityIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting cities', error: err });
        }
        return res.json({ success: true, message: 'Cities deleted successfully' });
    });
};

const getCitiesByStateId = (req, res) => {
    const { stateId } = req.params;
    Master.getCitiesByStateId(stateId, (err, results) => {
      if (err) {
        res.status(500).json({ success: false, message: 'Error fetching cities', error: err });
      } else {
        res.status(200).json({ success: true, cities: results });
      }
    });
  };

const deleteStates = (req, res) => {
    const { stateIds } = req.body;
    if (!Array.isArray(stateIds) || stateIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid city IDs' });
    }

    Master.deleteStates(stateIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting cities', error: err });
        }
        return res.json({ success: true, message: 'Cities deleted successfully' });
    });
};








// Blood Group Controllers
const addBloodGroup = (req, res) => {
    const { name } = req.body;
    const created_by = req.session.userId;
    Master.createBloodGroup(name, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding blood group', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Blood group added successfully' });
        }
    });
};

const getAllBloodGroups = (req, res) => {
    Master.getAllBloodGroups((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching blood groups', error: err });
        } else {
            res.status(200).json({ success: true, bloodGroups: results });
        }
    });
};

const getBloodGroupById = (req, res) => {
    const { id } = req.params;
    Master.getBloodGroupById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching blood group', error: err });
        } else {
            res.status(200).json({ success: true, bloodGroup: result });
        }
    });
};

const updateBloodGroup = (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    const updated_by = req.session.userId;
    Master.updateBloodGroup(id, name, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating blood group', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Blood group updated successfully' });
        }
    });
};

const deleteBloodGroup = (req, res) => {
    const { id } = req.params;
    Master.deleteBloodGroup(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting blood group', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Blood group deleted successfully' });
        }
    });
};

const deleteBloodGroups = (req, res) => {
  const { bloodGroupIds } = req.body;
  if (!Array.isArray(bloodGroupIds) || bloodGroupIds.length === 0) {
    return res.status(400).json({ success: false, message: 'Invalid blood group IDs' });
  }

  Master.deleteBloodGroups(bloodGroupIds, (err) => {
    if (err) {
      return res.status(500).json({ success: false, message: 'Error deleting blood groups', error: err });
    }
    return res.json({ success: true, message: 'Blood groups deleted successfully' });
  });
};



// Religion Controllers
const addReligion = (req, res) => {
    const { name } = req.body;
    const created_by = req.session.userId;
    Master.createReligion(name, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding religion', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Religion added successfully' });
        }
    });
};

const getAllReligions = (req, res) => {
    Master.getAllReligions((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching religions', error: err });
        } else {
            res.status(200).json({ success: true, religions: results });
        }
    });
};

const getReligionById = (req, res) => {
    const { id } = req.params;
    Master.getReligionById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching religion', error: err });
        } else {
            res.status(200).json({ success: true, religion: result });
        }
    });
};

const updateReligion = (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    const updated_by = req.session.userId;
    Master.updateReligion(id, name, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating religion', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Religion updated successfully' });
        }
    });
};

const deleteReligion = (req, res) => {
    const { id } = req.params;
    Master.deleteReligion(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting religion', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Religion deleted successfully' });
        }
    });
};

const deleteReligions = (req, res) => {
    const { religionIds } = req.body;
    if (!Array.isArray(religionIds) || religionIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid religion IDs' });
    }

    Master.deleteReligions(religionIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting religions', error: err });
        }
        return res.json({ success: true, message: 'Religions deleted successfully' });
    });
};



// Community Controllers
const addCommunity = (req, res) => {
    const { name } = req.body;
    const created_by = req.session.userId;
    Master.createCommunity(name, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding community', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Community added successfully' });
        }
    });
};

const getAllCommunities = (req, res) => {
    Master.getAllCommunities((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching communities', error: err });
        } else {
            res.status(200).json({ success: true, communities: results });
        }
    });
};

const getCommunityById = (req, res) => {
    const { id } = req.params;
    Master.getCommunityById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching community', error: err });
        } else {
            res.status(200).json({ success: true, community: result });
        }
    });
};

const updateCommunity = (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    const updated_by = req.session.userId;
    Master.updateCommunity(id, name, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating community', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Community updated successfully' });
        }
    });
};

const deleteCommunity = (req, res) => {
    const { id } = req.params;
    Master.deleteCommunity(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting community', error: err });
        } else {
            res.status(200).json({ success: true, message: 'Community deleted successfully' });
        }
    });
};

const deleteCommunities = (req, res) => {
    const { communityIds } = req.body;
    if (!Array.isArray(communityIds) || communityIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid community IDs' });
    }

    Master.deleteCommunities(communityIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting communities', error: err });
        }
        return res.json({ success: true, message: 'Communities deleted successfully' });
    });
};



// Designation Controllers
const addDesignation = (req, res) => {
    const { name, description } = req.body;
    const created_by = req.session.userId;
    Master.createDesignation(name, description, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding designation', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Designation added successfully', designationId: result.insertId });
        }
    });
};

const getAllDesignations = (req, res) => {
    Master.getAllDesignations((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching designations', error: err });
        } else {
            res.status(200).json({ success: true, designations: results });
        }
    });
};

const getDesignationById = (req, res) => {
    const { id } = req.params;
    Master.getDesignationById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching designation', error: err });
        } else {
            if (result.length === 0) {
                res.status(404).json({ success: false, message: 'Designation not found' });
            } else {
                res.status(200).json({ success: true, designation: result[0] });
            }
        }
    });
};

const updateDesignation = (req, res) => {
    const { id } = req.params;
    const { name, description, is_active } = req.body;
    const updated_by = req.session.userId;
    Master.updateDesignation(id, name, description, is_active, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating designation', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Designation not found' });
            } else {
                res.status(200).json({ success: true, message: 'Designation updated successfully' });
            }
        }
    });
};

const deleteDesignation = (req, res) => {
    const { id } = req.params;
    Master.deleteDesignation(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting designation', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Designation not found' });
            } else {
                res.status(200).json({ success: true, message: 'Designation deleted successfully' });
            }
        }
    });
};

const deleteDesignations = (req, res) => {
    const { designationIds } = req.body;
    if (!Array.isArray(designationIds) || designationIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid designation IDs' });
    }

    Master.deleteDesignations(designationIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting designations', error: err });
        }
        return res.json({ success: true, message: 'Designations deleted successfully' });
    });
};



const addDepartment = (req, res) => {
    const { name, is_active } = req.body;
    const created_by = req.session.userId;
    console.log(req.body);
    Master.createDepartment(name, is_active, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding department', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Department added successfully', departmentId: result.insertId });
        }
    });
};

const getAllDepartments = (req, res) => {
    Master.getAllDepartments((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching departments', error: err });
        } else {
            res.status(200).json({ success: true, departments: results });
        }
    });
};

const getDepartmentById = (req, res) => {
    const { id } = req.params;
    Master.getDepartmentById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching department', error: err });
        } else {
            if (result.length === 0) {
                res.status(404).json({ success: false, message: 'Department not found' });
            } else {
                res.status(200).json({ success: true, department: result[0] });
            }
        }
    });
};

const updateDepartment = (req, res) => {
    const { id } = req.params;
    const { name, is_active } = req.body;
    const updated_by = req.session.userId;
    Master.updateDepartment(id, name, is_active, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating department', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Department not found' });
            } else {
                res.status(200).json({ success: true, message: 'Department updated successfully' });
            }
        }
    });
};

const deleteDepartment = (req, res) => {
    const { id } = req.params;
    Master.deleteDepartment(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting department', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Department not found' });
            } else {
                res.status(200).json({ success: true, message: 'Department deleted successfully' });
            }
        }
    });
};

const deleteDepartments = (req, res) => {
    const { departmentIds } = req.body;
    if (!Array.isArray(departmentIds) || departmentIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid department IDs' });
    }

    Master.deleteDepartments(departmentIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting departments', error: err });
        }
        return res.json({ success: true, message: 'Departments deleted successfully' });
    });
};

const toggleDepartmentStatus = (req, res) => {
    const { id } = req.params;
    const updated_by = req.session.userId;
    Master.toggleDepartmentStatus(id, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error toggling department status', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Department not found' });
            } else {
                res.status(200).json({ success: true, message: 'Department status toggled successfully' });
            }
        }
    });
};



// Employment Type Controllers
const addEmploymentType = (req, res) => {
    const { name, description } = req.body;
    const created_by = req.session.userId;
    Master.createEmploymentType(name, description, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding employment type', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Employment type added successfully', employmentTypeId: result.insertId });
        }
    });
};

const getAllEmploymentTypes = (req, res) => {
    Master.getAllEmploymentTypes((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching employment types', error: err });
        } else {
            res.status(200).json({ success: true, employmentTypes: results });
        }
    });
};

const getEmploymentTypeById = (req, res) => {
    const { id } = req.params;
    Master.getEmploymentTypeById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching employment type', error: err });
        } else {
            if (result.length === 0) {
                res.status(404).json({ success: false, message: 'Employment type not found' });
            } else {
                res.status(200).json({ success: true, employmentType: result[0] });
            }
        }
    });
};

const updateEmploymentType = (req, res) => {
    const { id } = req.params;
    const { name, description, is_active } = req.body;
    const updated_by = req.session.userId;
    Master.updateEmploymentType(id, name, description, is_active, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating employment type', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Employment type not found' });
            } else {
                res.status(200).json({ success: true, message: 'Employment type updated successfully' });
            }
        }
    });
};

const deleteEmploymentType = (req, res) => {
    const { id } = req.params;
    Master.deleteEmploymentType(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting employment type', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Employment type not found' });
            } else {
                res.status(200).json({ success: true, message: 'Employment type deleted successfully' });
            }
        }
    });
};

const deleteEmploymentTypes = (req, res) => {
    const { employmentTypeIds } = req.body;
    if (!Array.isArray(employmentTypeIds) || employmentTypeIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid employment type IDs' });
    }

    Master.deleteEmploymentTypes(employmentTypeIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting employment types', error: err });
        }
        return res.json({ success: true, message: 'Employment types deleted successfully' });
    });
};

const toggleEmploymentTypeStatus = (req, res) => {
    const { id } = req.params;
    const updated_by = req.session.userId;
    Master.toggleEmploymentTypeStatus(id, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error toggling employment type status', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Employment type not found' });
            } else {
                res.status(200).json({ success: true, message: 'Employment type status toggled successfully' });
            }
        }
    });
};


// Gender Controllers
const addGender = (req, res) => {
    const { name, is_active } = req.body;
    const created_by = req.session.userId;
    Master.createGender(name, is_active, created_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error adding gender', error: err });
        } else {
            res.status(201).json({ success: true, message: 'Gender added successfully', genderId: result.insertId });
        }
    });
};

const getAllGenders = (req, res) => {
    Master.getAllGenders((err, results) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching genders', error: err });
        } else {
            res.status(200).json({ success: true, genders: results });
        }
    });
};

const getGenderById = (req, res) => {
    const { id } = req.params;
    Master.getGenderById(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error fetching gender', error: err });
        } else {
            if (result.length === 0) {
                res.status(404).json({ success: false, message: 'Gender not found' });
            } else {
                res.status(200).json({ success: true, gender: result[0] });
            }
        }
    });
};

const updateGender = (req, res) => {
    const { id } = req.params;
    const { name, is_active } = req.body;
    const updated_by = req.session.userId;
    Master.updateGender(id, name, is_active, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error updating gender', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Gender not found' });
            } else {
                res.status(200).json({ success: true, message: 'Gender updated successfully' });
            }
        }
    });
};

const deleteGender = (req, res) => {
    const { id } = req.params;
    Master.deleteGender(id, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error deleting gender', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Gender not found' });
            } else {
                res.status(200).json({ success: true, message: 'Gender deleted successfully' });
            }
        }
    });
};

const deleteGenders = (req, res) => {
    const { genderIds } = req.body;
    if (!Array.isArray(genderIds) || genderIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid gender IDs' });
    }

    Master.deleteGenders(genderIds, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting genders', error: err });
        }
        return res.json({ success: true, message: 'Genders deleted successfully' });
    });
};

const toggleGenderStatus = (req, res) => {
    const { id } = req.params;
    const updated_by = req.session.userId;
    Master.toggleGenderStatus(id, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({ success: false, message: 'Error toggling gender status', error: err });
        } else {
            if (result.affectedRows === 0) {
                res.status(404).json({ success: false, message: 'Gender not found' });
            } else {
                res.status(200).json({ success: true, message: 'Gender status toggled successfully' });
            }
        }
    });
};


const addStore = (req, res) => {
    console.log('Received data:', req.body);
    
    const storeData = {
        name: req.body.name,
        address: req.body.address,
        city_id: req.body.city_id,
        state_id: req.body.state_id,
        postal_code: req.body.postal_code,
        phone_number: req.body.phone_number,
        email: req.body.email,
        opening_hours: req.body.opening_hours,
        closing_hours: req.body.closing_hours,
        manager_id: req.body.manager_id,
        is_active: req.body.is_active === true || req.body.is_active === 1 || req.body.is_active === '1',
        created_by: req.session.userId
    };

    console.log('Processed store data:', storeData);

    Master.createStore(storeData, (err, result) => {
        if (err) {
            console.error('Error in createStore:', err);
            return res.status(500).json({
                success: false,
                message: 'Error adding store',
                error: err.message
            });
        }
        return res.status(201).json({
            success: true,
            message: 'Store added successfully',
            storeId: result.insertId
        });
    });
};
const getAllStores = (req, res) => {
    Master.getAllStores((err, results) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error fetching stores',
                error: err
            });
        } else {
            res.status(200).json({
                success: true,
                stores: results.map((store, index) => ({
                    ...store,
                    sl_no: index + 1
                }))
            });
        }
    });
};

const getStoreById = (req, res) => {
    const { id } = req.params;
    Master.getStoreById(id, (err, result) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error fetching store',
                error: err
            });
        } else if (result.length === 0) {
            res.status(404).json({
                success: false,
                message: 'Store not found'
            });
        } else {
            res.status(200).json({
                success: true,
                store: result[0]
            });
        }
    });
};

const updateStore = (req, res) => {
    const { id } = req.params;
    const storeData = {
        ...req.body,
        updated_by: req.session.userId
    };

    Master.updateStore(id, storeData, (err, result) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error updating store',
                error: err
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({
                success: false,
                message: 'Store not found'
            });
        } else {
            res.status(200).json({
                success: true,
                message: 'Store updated successfully'
            });
        }
    });
};

const deleteStore = (req, res) => {
    const { id } = req.params;
    Master.deleteStore(id, (err, result) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error deleting store',
                error: err
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({
                success: false,
                message: 'Store not found'
            });
        } else {
            res.status(200).json({
                success: true,
                message: 'Store deleted successfully'
            });
        }
    });
};

const deleteStores = (req, res) => {
    const { storeIds } = req.body;
    if (!Array.isArray(storeIds) || storeIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: 'Invalid store IDs'
        });
    }

    Master.deleteStores(storeIds, (err) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Error deleting stores',
                error: err
            });
        }
        return res.json({
            success: true,
            message: 'Stores deleted successfully'
        });
    });
};

const toggleStoreStatus = (req, res) => {
    const { id } = req.params;
    const updated_by = req.session.userId;
    
    Master.toggleStoreStatus(id, updated_by, (err, result) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error toggling store status',
                error: err
            });
        } else if (result.affectedRows === 0) {
            res.status(404).json({
                success: false,
                message: 'Store not found'
            });
        } else {
            res.status(200).json({
                success: true,
                message: 'Store status toggled successfully'
            });
        }
    });
};

const getAllManagers = (req, res) => {
    Master.getAllManagers((err, results) => {
        if (err) {
            res.status(500).json({
                success: false,
                message: 'Error fetching managers',
                error: err
            });
        } else {
            res.status(200).json({
                success: true,
                managers: results.map((manager, index) => ({
                    ...manager,
                    sl_no: index + 1
                }))
            });
        }
    });
}

// Add to masterController.js

const assignStaffToStore = (req, res) => {
    const { staff_id, store_id } = req.body;
    const created_by = req.session.userId;

    // First check if mapping already exists
    Master.getSpecificStaffStoreMapping(staff_id, store_id, (err, existing) => {
        if (err) {
            return res.status(500).json({ 
                success: false, 
                message: 'Error checking existing mapping', 
                error: err 
            });
        }

        if (existing && existing.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'Staff is already assigned to this store'
            });
        }

        const mappingData = {
            staff_id,
            store_id,
            created_by
        };

        Master.assignStaffToStore(mappingData, (err, result) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error assigning staff to store', 
                    error: err 
                });
            }
            
            res.status(201).json({ 
                success: true, 
                message: 'Staff assigned to store successfully',
                data: result 
            });
        });
    });
};

const bulkAssignStaffToStore = (req, res) => {
    const { mappings } = req.body;
    const created_by = req.session.userId;

    if (!Array.isArray(mappings) || mappings.length === 0) {
        return res.status(400).json({
            success: false,
            message: 'Invalid or empty mappings array'
        });
    }

    // Add created_by to each mapping
    const mappingsWithCreator = mappings.map(mapping => ({
        ...mapping,
        created_by
    }));

    Master.bulkAssignStaffToStore(mappingsWithCreator, (err, result) => {
        if (err) {
            // Check for duplicate entry error
            if (err.code === 'ER_DUP_ENTRY') {
                return res.status(400).json({
                    success: false,
                    message: 'One or more staff members are already assigned to the specified stores'
                });
            }
            
            return res.status(500).json({ 
                success: false, 
                message: 'Error assigning staff to stores', 
                error: err 
            });
        }

        res.status(201).json({ 
            success: true, 
            message: 'Staff assigned to stores successfully',
            data: result 
        });
    });
};


const getStoreStaff = (req, res) => {
    const { storeId } = req.params;
    
    Master.getStoreStaff(storeId, (err, results) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Error fetching store staff',
                error: err
            });
        }
        
        return res.status(200).json({
            success: true,
            staff: results
        });
    });
};

const getStaffStores = (req, res) => {
    const { staff_id } = req.params;

    if (!staff_id) {
        return res.status(400).json({
            success: false,
            message: 'Staff ID is required'
        });
    }
    
    Master.getStaffStores(staff_id, (err, results) => {
        if (err) {
            return res.status(500).json({ 
                success: false, 
                message: 'Error fetching staff stores', 
                error: err 
            });
        }

        res.status(200).json({ 
            success: true, 
            staffStores: results 
        });
    });
};

const removeStaffFromStore = (req, res) => {
    const { staff_id, store_id } = req.params;

    if (!staff_id || !store_id) {
        return res.status(400).json({
            success: false,
            message: 'Both Staff ID and Store ID are required'
        });
    }

    Master.removeStaffFromStore(staff_id, store_id, (err, result) => {
        if (err) {
            return res.status(500).json({ 
                success: false, 
                message: 'Error removing staff from store', 
                error: err 
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'No mapping found for the specified staff and store'
            });
        }

        res.status(200).json({ 
            success: true, 
            message: 'Staff removed from store successfully' 
        });
    });
};

const updateStaffStoreStatus = (req, res) => {
    const { mapping_id } = req.params;
    const { is_active } = req.body;
    const updated_by = req.session.userId;

    if (is_active === undefined) {
        return res.status(400).json({
            success: false,
            message: 'Active status is required'
        });
    }

    Master.updateStaffStoreStatus(mapping_id, is_active, updated_by, (err, result) => {
        if (err) {
            return res.status(500).json({ 
                success: false, 
                message: 'Error updating staff store status', 
                error: err 
            });
        }

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Mapping not found'
            });
        }

        res.status(200).json({ 
            success: true, 
            message: 'Staff store status updated successfully' 
        });
    });
};

const getUnassignedStaff = (req, res) => {    

    Master.getUnassignedStaff((err, results) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Error fetching unassigned staff',
                error: err
            });
        }
        res.status(200).json({
            success: true,
            staff: results
        });
    });
};



const bulkRemoveStaffFromStore = (req, res) => {
    const { mappingIds } = req.body;
    if (!Array.isArray(mappingIds) || mappingIds.length === 0) {
        return res.status(400).json({ success: false, message: 'Invalid mapping IDs' });
    }

    Master.bulkRemoveStaffFromStore(mappingIds, (err, result) => {
        if (err) {
            return res.status(500).json({ 
                success: false, 
                message: 'Error removing staff members', 
                error: err 
            });
        }
        return res.json({ 
            success: true, 
            message: 'Staff members removed successfully',
            affectedRows: result.affectedRows 
        });
    });
};







    








module.exports = { 
    // State
    addState,
    getAllStates,
    getStateById,
    updateState,
    deleteState,
    deleteStates,

    // City
    addCity,
    getAllCities,
    getCityById,
    updateCity,
    deleteCity,
    deleteCities, 
    getCitiesByStateId,

    // Store
    addStore,
    getAllStores,
    getStoreById,
    updateStore,
    deleteStore,
    deleteStores,
    toggleStoreStatus,
    getAllManagers,

    // Blood Group
    addBloodGroup,
    getAllBloodGroups,
    getBloodGroupById,
    updateBloodGroup,
    deleteBloodGroup,
    deleteBloodGroups,

    // Religion
    addReligion,
    getAllReligions,
    getReligionById,
    updateReligion,
    deleteReligion,
    deleteReligions,

    // Community
    addCommunity,
    getAllCommunities,
    getCommunityById,
    updateCommunity,
    deleteCommunity,
    deleteCommunities,

    // Designation
    addDesignation,
    getAllDesignations,
    getDesignationById,
    updateDesignation,
    deleteDesignation,
    deleteDesignations,

    // Department
    addDepartment,
    getAllDepartments,
    getDepartmentById,
    updateDepartment,
    deleteDepartment,
    deleteDepartments,
    toggleDepartmentStatus,

    // Employment Type
    addEmploymentType,
    getAllEmploymentTypes,
    getEmploymentTypeById,
    updateEmploymentType,
    deleteEmploymentType,
    deleteEmploymentTypes,
    toggleEmploymentTypeStatus,

    // Gender
    addGender,
    getAllGenders,
    getGenderById,
    updateGender,
    deleteGender,
    deleteGenders,
    toggleGenderStatus,

    // Staff-Store Mapping
    assignStaffToStore,
    bulkAssignStaffToStore,
    getStoreStaff,
    getStaffStores,
    removeStaffFromStore,
    updateStaffStoreStatus,
    bulkRemoveStaffFromStore, 
    getUnassignedStaff,

    
    




    
};