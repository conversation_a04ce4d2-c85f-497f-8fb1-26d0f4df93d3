// controllers/publicController.js

const Interview = require('../models/interviewModel');
const RegistrationToken = require('../models/registrationTokenModel');
const path = require('path');
const fs = require('fs');

// Utility function for deleting files
const deleteFile = (filePath) => {
    if (!filePath) return;
    
    fs.unlink(filePath, (err) => {
        if (err) console.error('Error deleting file:', err);
    });
};

const publicController = {
    // Register a candidate using a token
    registerCandidate: (req, res) => {
        const { token } = req.params;
        
        // Validate the token
        RegistrationToken.validateToken(token, (tokenErr, tokenResult) => {
            if (tokenErr || tokenResult.length === 0 || !tokenResult[0].is_valid) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid or expired registration token'
                });
            }
            
            const tokenData = tokenResult[0];
            
            // Handle file uploads
            let resumeFileName = null;
            let profilePictureFileName = null;
            
            if (req.files) {
                if (req.files.resumeFile) {
                    resumeFileName = req.files.resumeFile[0].filename;
                }
                if (req.files.profilePicture) {
                    profilePictureFileName = req.files.profilePicture[0].filename;
                }
            }
            
            // Extract siblings data if exists
            let siblings;
            if (req.body.siblings) {
                try {
                    siblings = typeof req.body.siblings === 'string' 
                        ? JSON.parse(req.body.siblings) 
                        : req.body.siblings;
                } catch (e) {
                    console.error('Error parsing siblings:', e);
                    siblings = [];
                }
            }
            
            // Prepare candidate data (excluding siblings)
            const candidateData = { ...req.body };
            delete candidateData.siblings; // Remove siblings to prevent direct insertion
            
            // Add file paths and metadata
            candidateData.created_by = tokenData.created_by;
            candidateData.resumeFile = resumeFileName;
            candidateData.profilePicture = profilePictureFileName;
            
            // Create the candidate record
            Interview.createCandidate(candidateData, (err, result) => {
                if (err) {
                    // Clean up files if there was an error creating the candidate
                    if (resumeFileName) {
                        const filePath = path.join(__dirname, '../uploads', resumeFileName);
                        deleteFile(filePath);
                    }
                    
                    if (profilePictureFileName) {
                        const filePath = path.join(__dirname, '../uploads', profilePictureFileName);
                        deleteFile(filePath);
                    }
                    
                    return res.status(500).json({
                        success: false,
                        message: 'Error registering candidate',
                        error: err
                    });
                }
                
                const candidateId = result.insertId;
                
                // Handle siblings if provided
                if (siblings && Array.isArray(siblings) && siblings.length > 0) {
                    const siblingPromises = siblings.map(sibling => {
                        return new Promise((resolve, reject) => {
                            const siblingData = {
                                candidate_id: candidateId,
                                name: sibling.name,
                                date_of_birth: sibling.date_of_birth ? new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                gender: sibling.gender,
                                occupation: sibling.occupation || null,
                                marital_status: sibling.marital_status || 'Single',
                                contact: sibling.contact || null,
                                status: sibling.status || 'Living',
                                is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                additional_info: sibling.additional_info || null,
                                created_by: tokenData.created_by
                            };

                            Interview.createCandidateSibling(siblingData, (err, result) => {
                                if (err) reject(err);
                                else resolve(result);
                            });
                        });
                    });

                    Promise.all(siblingPromises)
                        .then(() => {
                            handlePositionAssignment(candidateId, tokenData, token, res);
                        })
                        .catch(error => {
                            console.error('Error creating siblings:', error);
                            handlePositionAssignment(candidateId, tokenData, token, res, error);
                        });
                } else {
                    handlePositionAssignment(candidateId, tokenData, token, res);
                }
            });
        });
    },
    
    // Validate a registration token
    validateToken: (req, res) => {
        const { token } = req.params;
        
        RegistrationToken.validateToken(token, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error validating token',
                    error: err
                });
            }
            
            if (result.length === 0 || !result[0].is_valid) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid or expired registration token'
                });
            }
            
            // If token is valid, return position details if associated with a position
            const tokenData = result[0];
            if (tokenData.position_id) {
                Interview.getPositionById(tokenData.position_id, (posErr, position) => {
                    if (posErr || position.length === 0) {
                        return res.status(200).json({
                            success: true,
                            token: tokenData,
                            message: 'Valid registration token'
                        });
                    }
                    
                    res.status(200).json({
                        success: true,
                        token: tokenData,
                        position: position[0],
                        message: 'Valid registration token'
                    });
                });
            } else {
                res.status(200).json({
                    success: true,
                    token: tokenData,
                    message: 'Valid registration token'
                });
            }
        });
    },
    
    // Get open positions for public view
    getOpenPositions: (req, res) => {
        Interview.getOpenPositions((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching open positions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                positions: results
            });
        });
    }
};

// Helper function to handle position assignment and token usage
function handlePositionAssignment(candidateId, tokenData, token, res, siblingError = null) {
    // If position ID was provided in the token, assign the candidate to that position
    if (tokenData.position_id) {
        const mappingData = {
            candidate_id: candidateId,
            position_id: tokenData.position_id,
            created_by: tokenData.created_by
        };
        
        Interview.assignCandidateToPosition(mappingData, (mappingErr) => {
            if (mappingErr) {
                console.error('Error assigning candidate to position:', mappingErr);
            }
            
            // Mark the token as used
            RegistrationToken.markTokenAsUsed(token, (markErr) => {
                if (markErr) {
                    console.error('Error marking token as used:', markErr);
                }
                
                res.status(201).json({
                    success: true,
                    message: siblingError 
                        ? 'Registration successful but error with siblings' 
                        : 'Registration successful',
                    candidateId: candidateId,
                    siblingError: siblingError ? siblingError.message : null
                });
            });
        });
    } else {
        // Mark the token as used
        RegistrationToken.markTokenAsUsed(token, (markErr) => {
            if (markErr) {
                console.error('Error marking token as used:', markErr);
            }
            
            res.status(201).json({
                success: true,
                message: siblingError 
                    ? 'Registration successful but error with siblings' 
                    : 'Registration successful',
                candidateId: candidateId,
                siblingError: siblingError ? siblingError.message : null
            });
        });
    }
}

module.exports = publicController;