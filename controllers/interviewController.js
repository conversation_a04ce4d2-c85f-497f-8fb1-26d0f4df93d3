// interviewController.js - Main file that imports and exports all interview-related controllers

const positionController = require('./interview/positionController');
const candidateController = require('./interview/candidateController');
const siblingController = require('./interview/siblingController');
const interviewSessionController = require('./interview/interviewSessionController');
const decisionController = require('./interview/decisionController');
const dashboardController = require('./interview/dashboardController');
const candidateCommentController = require('./interview/candidateCommentController');
const registrationTokenController = require('./registrationTokenController');

// Combine all controllers into one object
const interviewController = {
    // Position Management
    addPosition: positionController.addPosition,
    getAllPositions: positionController.getAllPositions,
    getPositionById: positionController.getPositionById,
    updatePosition: positionController.updatePosition,
    deletePosition: positionController.deletePosition,
    deletePositions: positionController.deletePositions,
    togglePositionStatus: positionController.togglePositionStatus,
    updateVacancies: positionController.updateVacancies,
    getPositionsByDepartment: positionController.getPositionsByDepartment,
    getOpenPositions: positionController.getOpenPositions,

    // Candidate Management
    addCandidate: candidateController.addCandidate,
    getAllCandidates: candidateController.getAllCandidates,
    getCandidateById: candidateController.getCandidateById,
    updateCandidate: candidateController.updateCandidate,
    deleteCandidate: candidateController.deleteCandidate,
    deleteCandidates: candidateController.deleteCandidates,
    updateCandidateStatus: candidateController.updateCandidateStatus,

    // Siblings Management
    addCandidateSibling: siblingController.addSibling,
    updateCandidateSibling: siblingController.updateSibling,
    deleteCandidateSibling: siblingController.deleteSibling,

    // Candidate Position Mapping
    assignCandidateToPosition: candidateController.assignCandidateToPosition,
    getCandidatePositions: candidateController.getCandidatePositions,
    getPositionCandidates: candidateController.getPositionCandidates,
    updateCandidatePositionStatus: candidateController.updateCandidatePositionStatus,
    removeCandidateFromPosition: candidateController.removeCandidateFromPosition,

    // Interview Session Management
    addInterviewSession: interviewSessionController.addInterviewSession,
    getInterviewSessions: interviewSessionController.getInterviewSessions,
    getInterviewSessionById: interviewSessionController.getInterviewSessionById,
    updateInterviewSession: interviewSessionController.updateInterviewSession,
    deleteInterviewSession: interviewSessionController.deleteInterviewSession,
    getAllInterviewSessions: interviewSessionController.getAllInterviewSessions,

      // Candidate Comments - Add this section
    addOrUpdateComment: candidateCommentController.addOrUpdateComment,
    getCandidateComments: candidateCommentController.getCandidateComments,
    getFieldComments: candidateCommentController.getFieldComments,
    markCommentAsRead: candidateCommentController.markCommentAsRead,
    deleteComment: candidateCommentController.deleteComment,

    // Candidate Decision Management
    addCandidateDecision: decisionController.addCandidateDecision,
    getCandidateDecision: decisionController.getCandidateDecision,
    updateCandidateDecision: decisionController.updateCandidateDecision,
    setStaffForCandidate: decisionController.setStaffForCandidate,

    // Search and Reporting
    searchCandidates: candidateController.searchCandidates,

    // Registration Token Management
    generateToken: registrationTokenController.generateToken,
    getAllTokens: registrationTokenController.getAllTokens,
    getMyTokens: registrationTokenController.getMyTokens,
    invalidateToken: registrationTokenController.invalidateToken,
    deleteToken: registrationTokenController.deleteToken,

    // Dashboard
    getDashboardStats: dashboardController.getDashboardStats,
    getRecentCandidates: dashboardController.getRecentCandidates,
    getUpcomingSessions: dashboardController.getUpcomingSessions
};

module.exports = interviewController;