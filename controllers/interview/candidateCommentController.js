// controllers/interview/candidateCommentController.js

const CandidateComment = require('../../models/interview/candidateCommentModel');

const candidateCommentController = {
  // Add a new comment or update an existing one

  // Modified addOrUpdateComment method
addOrUpdateComment: (req, res) => {
  const { candidate_id, field_identifier, comment_text } = req.body;
  const user_id = req.session.userId;

  if (!candidate_id || !field_identifier || !comment_text) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields: candidate_id, field_identifier, and comment_text are required'
    });
  }

  // Always create a new comment instead of updating existing ones
  const commentData = {
    candidate_id,
    field_identifier,
    comment_text,
    user_id
  };

  CandidateComment.createComment(commentData, (insertErr, result) => {
    if (insertErr) {
      console.error('Error adding comment:', insertErr);
      return res.status(500).json({
        success: false,
        message: 'Error adding comment',
        error: insertErr
      });
    }

    // Get the new comment details
    CandidateComment.getCommentById(result.insertId, (getErr, comment) => {
      if (getErr) {
        console.error('Error fetching new comment:', getErr);
        return res.status(500).json({
          success: false,
          message: 'Error fetching new comment',
          error: getErr
        });
      }

      res.status(201).json({
        success: true,
        message: 'Comment added successfully',
        comment: comment[0]
      });
    });
  });
},
 

  // Get all comments for a candidate
  getCandidateComments: (req, res) => {
    const { candidate_id } = req.params;

    CandidateComment.getCandidateComments(candidate_id, (err, results) => {
      if (err) {
        console.error('Error fetching candidate comments:', err);
        return res.status(500).json({
          success: false,
          message: 'Error fetching candidate comments',
          error: err
        });
      }

      res.status(200).json({
        success: true,
        comments: results
      });
    });
  },

  // Get comments for a specific field
  getFieldComments: (req, res) => {
    const { candidate_id, field_identifier } = req.params;

    CandidateComment.getFieldComments(candidate_id, field_identifier, (err, results) => {
      if (err) {
        console.error('Error fetching field comments:', err);
        return res.status(500).json({
          success: false,
          message: 'Error fetching field comments',
          error: err
        });
      }

      res.status(200).json({
        success: true,
        comments: results
      });
    });
  },

  // Mark a comment as read
  markCommentAsRead: (req, res) => {
    const { comment_id } = req.params;

    CandidateComment.markAsRead(comment_id, (err, result) => {
      if (err) {
        console.error('Error marking comment as read:', err);
        return res.status(500).json({
          success: false,
          message: 'Error marking comment as read',
          error: err
        });
      }

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Comment not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Comment marked as read',
        comment_id: parseInt(comment_id)
      });
    });
  },

  // Delete a comment (soft delete)
  deleteComment: (req, res) => {
    const { comment_id } = req.params;

    CandidateComment.deleteComment(comment_id, (err, result) => {
      if (err) {
        console.error('Error deleting comment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error deleting comment',
          error: err
        });
      }

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Comment not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Comment deleted successfully',
        comment_id: parseInt(comment_id)
      });
    });
  }
};

module.exports = candidateCommentController;