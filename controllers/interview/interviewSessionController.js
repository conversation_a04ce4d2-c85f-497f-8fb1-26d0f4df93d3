// controllers/interview/interviewSessionController.js

const Interview = require('../../models/interviewModel');

const interviewSessionController = {
    // Add an interview session
    addInterviewSession: (req, res) => {
        const sessionData = {
            ...req.body,
            interviewer_id: req.body.interviewer_id || req.session.userId // Default to current user if not specified
        };
        
        Interview.createInterviewSession(sessionData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error adding interview session',
                    error: err
                });
            }
            
            res.status(201).json({
                success: true,
                message: 'Interview session added successfully',
                sessionId: result.insertId
            });
        });
    },

    // Get interview sessions for a candidate and position
    getInterviewSessions: (req, res) => {
        const { candidateId, positionId } = req.params;
        
        Interview.getInterviewSessions(candidateId, positionId, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching interview sessions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                sessions: results
            });
        });
    },

    // Get a specific interview session by ID
    getInterviewSessionById: (req, res) => {
        const { id } = req.params;
        
        Interview.getInterviewSessionById(id, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching interview session',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Interview session not found'
                });
            }
            
            res.status(200).json({
                success: true,
                session: results[0]
            });
        });
    },

    // Update an interview session
    updateInterviewSession: (req, res) => {
        const { id } = req.params;
        
        // Log for debugging
        console.log('Session info:', req.session);
        
        // Create session data with safer access to userId
        const sessionData = {
            ...req.body,
            updated_by: req.session && req.session.userId ? req.session.userId : null
        };
        
        console.log('Session data to be updated:', sessionData);
        
        Interview.updateInterviewSession(id, sessionData, (err, result) => {
            if (err) {
                console.error('Error in updateInterviewSession:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error updating interview session',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Interview session not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Interview session updated successfully'
            });
        });
    },

    // Delete an interview session
    deleteInterviewSession: (req, res) => {
        const { id } = req.params;
        
        Interview.deleteInterviewSession(id, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting interview session',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Interview session not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Interview session deleted successfully'
            });
        });
    },

    // Get all interview sessions
    getAllInterviewSessions: (req, res) => {
        Interview.getAllInterviewSessions((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching interview sessions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                sessions: results
            });
        });
    },

    // Get upcoming interview sessions
    getUpcomingSessions: (req, res) => {
        const limit = req.query.limit ? parseInt(req.query.limit) : 5;
        
        Interview.getUpcomingSessions(limit, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching upcoming interview sessions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                sessions: results
            });
        });
    },

    // Get dashboard statistics
    getDashboardStats: (req, res) => {
        Interview.getDashboardStats((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching dashboard statistics',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Statistics not found'
                });
            }
            
            res.status(200).json({
                success: true,
                stats: results[0]
            });
        });
    }
};

module.exports = interviewSessionController;