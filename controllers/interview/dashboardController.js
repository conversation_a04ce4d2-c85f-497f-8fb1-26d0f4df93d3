// controllers/interview/dashboardController.js

const Interview = require('../../models/interviewModel');

const dashboardController = {
    // Get general dashboard statistics
    getDashboardStats: (req, res) => {
        Interview.getDashboardStats((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching dashboard statistics',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Statistics not found'
                });
            }
            
            res.status(200).json({
                success: true,
                stats: results[0]
            });
        });
    },

    // Get recent candidates
    getRecentCandidates: (req, res) => {
        const limit = req.query.limit ? parseInt(req.query.limit) : 5;
        
        Interview.getRecentCandidates(limit, (err, results) => {
            if (err) {
                console.error('Error fetching recent candidates:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching recent candidates',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                candidates: results || [] // Ensure we always return an array even if results is undefined
            });
        });
    },

    // Get upcoming interview sessions
    getUpcomingSessions: (req, res) => {
        const limit = req.query.limit ? parseInt(req.query.limit) : 5;
        
        Interview.getUpcomingSessions(limit, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching upcoming interview sessions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                sessions: results
            });
        });
    },
    
    // Get position statistics
    getPositionStats: (req, res) => {
        Interview.getPositionStats((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching position statistics',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                stats: results
            });
        });
    },
    
    // Get hiring pipeline statistics
    getHiringPipelineStats: (req, res) => {
        Interview.getHiringPipelineStats((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching hiring pipeline statistics',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                stats: results
            });
        });
    },
    
    // Get monthly hiring statistics
    getMonthlyHiringStats: (req, res) => {
        const year = req.query.year ? parseInt(req.query.year) : new Date().getFullYear();
        
        Interview.getMonthlyHiringStats(year, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching monthly hiring statistics',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                year: year,
                stats: results
            });
        });
    },
    
    // Get department-wise candidate distribution
    getDepartmentCandidateStats: (req, res) => {
        Interview.getDepartmentCandidateStats((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching department-wise candidate statistics',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                stats: results
            });
        });
    },
    
    // Get all dashboard data in a single request
    getAllDashboardData: (req, res) => {
        const limit = req.query.limit ? parseInt(req.query.limit) : 5;
        const year = req.query.year ? parseInt(req.query.year) : new Date().getFullYear();
        
        // Get general stats
        Interview.getDashboardStats((statsErr, statsResults) => {
            if (statsErr) {
                console.error('Error fetching dashboard statistics:', statsErr);
            }
            
            // Get recent candidates
            Interview.getRecentCandidates(limit, (candidatesErr, candidatesResults) => {
                if (candidatesErr) {
                    console.error('Error fetching recent candidates:', candidatesErr);
                }
                
                // Get upcoming sessions
                Interview.getUpcomingSessions(limit, (sessionsErr, sessionsResults) => {
                    if (sessionsErr) {
                        console.error('Error fetching upcoming sessions:', sessionsErr);
                    }
                    
                    // Get position stats
                    Interview.getPositionStats((positionErr, positionResults) => {
                        if (positionErr) {
                            console.error('Error fetching position stats:', positionErr);
                        }
                        
                        // Get hiring pipeline stats
                        Interview.getHiringPipelineStats((pipelineErr, pipelineResults) => {
                            if (pipelineErr) {
                                console.error('Error fetching pipeline stats:', pipelineErr);
                            }
                            
                            // Get monthly stats
                            Interview.getMonthlyHiringStats(year, (monthlyErr, monthlyResults) => {
                                if (monthlyErr) {
                                    console.error('Error fetching monthly stats:', monthlyErr);
                                }
                                
                                // Get department stats
                                Interview.getDepartmentCandidateStats((deptErr, deptResults) => {
                                    if (deptErr) {
                                        console.error('Error fetching department stats:', deptErr);
                                    }
                                    
                                    // Compile all results
                                    const response = {
                                        success: true,
                                        generalStats: statsResults && statsResults.length > 0 ? statsResults[0] : null,
                                        recentCandidates: candidatesResults || [],
                                        upcomingSessions: sessionsResults || [],
                                        positionStats: positionResults || [],
                                        hiringPipelineStats: pipelineResults || [],
                                        monthlyStats: {
                                            year: year,
                                            data: monthlyResults || []
                                        },
                                        departmentStats: deptResults || []
                                    };
                                    
                                    res.status(200).json(response);
                                });
                            });
                        });
                    });
                });
            });
        });
    }
};

module.exports = dashboardController;