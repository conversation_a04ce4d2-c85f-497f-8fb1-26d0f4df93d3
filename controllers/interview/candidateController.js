// controllers/interview/candidateController.js

const Interview = require('../../models/interviewModel');
const path = require('path');
const fs = require('fs');

// Utility function for deleting files
const deleteFile = (filePath) => {
   if (!filePath) return;
   
   fs.unlink(filePath, (err) => {
       if (err) console.error('Error deleting file:', err);
   });
};

const candidateController = {
   // Add a new candidate
   addCandidate: (req, res) => {
       // Handle file uploads
       let resumeFileName = null;
       let profilePictureFileName = null;
       
       if (req.files) {
           if (req.files.resumeFile) {
               resumeFileName = req.files.resumeFile[0].filename;
           }
           if (req.files.profilePicture) {
               profilePictureFileName = req.files.profilePicture[0].filename;
           }
       }
       
       const candidateData = {
           // Personal Information
           firstName: req.body.firstName,
           lastName: req.body.lastName,
           dateOfBirth: req.body.dateOfBirth ? new Date(req.body.dateOfBirth).toISOString().split('T')[0] : null,
           genderId: req.body.genderId ? parseInt(req.body.genderId) : null,
           email: req.body.email,
           phoneNumber: req.body.phoneNumber,
           alternatePhone: req.body.alternatePhone || null,
           
           // Address Information
           address: req.body.address || null,
           permanentAddress: req.body.permanentAddress || null,
           city: req.body.city || null,
           state: req.body.state || null,
           postalCode: req.body.postalCode || null,
           
           // Emergency Contact Information
           emergencyContactName: req.body.emergencyContactName || null,
           emergencyContactNumber: req.body.emergencyContactNumber || null,
           
           // Professional Information
           totalExperience: req.body.totalExperience || null,
           currentOrganization: req.body.currentOrganization || null,
           currentDesignation: req.body.currentDesignation || null,
           currentSalary: req.body.currentSalary || null,
           expectedSalary: req.body.expectedSalary || null,
           noticePeriod: req.body.noticePeriod || null,
           reasonForChange: req.body.reasonForChange || null,
           skills: req.body.skills || null,
           
           // Department & Position Information (if joining directly)
           employeeId: req.body.employeeId || null,
           hireDate: req.body.hireDate ? new Date(req.body.hireDate).toISOString().split('T')[0] : null,
           departmentId: req.body.departmentId ? parseInt(req.body.departmentId) : null,
           designationId: req.body.designationId ? parseInt(req.body.designationId) : null,
           employmentTypeId: req.body.employmentTypeId ? parseInt(req.body.employmentTypeId) : null,
           salary: req.body.salary || null,
           
           // Education Information
           educationLevel: req.body.educationLevel || null,
           degrees: req.body.degrees || null,
           university: req.body.university || null,
           yearOfPassing: req.body.yearOfPassing ? parseInt(req.body.yearOfPassing) : null,
           specialization: req.body.specialization || null,
           additionalCertifications: req.body.additionalCertifications || null,
           
           // Additional Personal Details
           bloodGroupId: req.body.bloodGroupId ? parseInt(req.body.bloodGroupId) : null,
           religion: req.body.religion || null,
           community: req.body.community || null,
           idProofType: req.body.idProofType || null,
           idProofNumber: req.body.idProofNumber || null,
           idProof: req.body.idProof || null,
           
           // Family Information
           father_name: req.body.father_name || null,
           father_occupation: req.body.father_occupation || null,
           father_contact: req.body.father_contact || null,
           father_status: req.body.father_status || 'Living',
           mother_name: req.body.mother_name || null,
           mother_occupation: req.body.mother_occupation || null,
           mother_contact: req.body.mother_contact || null,
           mother_status: req.body.mother_status || 'Living',
           family_address: req.body.family_address || null,
           
           // Files and Links
           resumeFile: resumeFileName,
           profilePicture: profilePictureFileName,
           referenceContacts: req.body.referenceContacts || null,
           portfolioLinks: req.body.portfolioLinks || null,
           
           // Status Information
           status: req.body.status || 'New',
           isActive: req.body.isActive === true || req.body.isActive === "true" ? 1 : 0,
           employmentStatus: req.body.employmentStatus || null,
           
           // Metadata
           created_by: req.session.userId,
           updated_by: req.session.userId
       };
       
       // Create the candidate record
       Interview.createCandidate(candidateData, (err, result) => {
           if (err) {
               // Clean up files if there was an error creating the candidate
               if (resumeFileName) {
                   const filePath = path.join(__dirname, '../../uploads', resumeFileName);
                   deleteFile(filePath);
               }
               
               if (profilePictureFileName) {
                   const filePath = path.join(__dirname, '../../uploads', profilePictureFileName);
                   deleteFile(filePath);
               }
               
               return res.status(500).json({
                   success: false,
                   message: 'Error adding candidate',
                   error: err
               });
           }
           
           const candidateId = result.insertId;
           
           // Handle siblings if provided
           if (req.body.siblings) {
               let siblings;
               try {
                   siblings = typeof req.body.siblings === 'string' 
                       ? JSON.parse(req.body.siblings) 
                       : req.body.siblings;
               } catch (e) {
                   console.error('Error parsing siblings:', e);
                   siblings = [];
               }

               if (Array.isArray(siblings) && siblings.length > 0) {
                   const siblingPromises = siblings.map(sibling => {
                       return new Promise((resolve, reject) => {
                           const siblingData = {
                               candidate_id: candidateId,
                               name: sibling.name,
                               date_of_birth: sibling.date_of_birth ? new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                               gender: sibling.gender,
                               occupation: sibling.occupation || null,
                               marital_status: sibling.marital_status || 'Single',
                               contact: sibling.contact || null,
                               status: sibling.status || 'Living',
                               is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                               additional_info: sibling.additional_info || null,
                               created_by: req.session.userId
                           };

                           Interview.createCandidateSibling(siblingData, (err, result) => {
                               if (err) reject(err);
                               else resolve(result);
                           });
                       });
                   });

                   Promise.all(siblingPromises)
                       .then(() => {
                           candidateController.handlePositionAssignment(req, res, candidateId);
                       })
                       .catch(error => {
                           console.error('Error creating siblings:', error);
                           candidateController.handlePositionAssignment(req, res, candidateId, error);
                       });
               } else {
                   candidateController.handlePositionAssignment(req, res, candidateId);
               }
           } else {
               candidateController.handlePositionAssignment(req, res, candidateId);
           }
       });
   },

   // Helper function for position assignment after candidate creation
   handlePositionAssignment: (req, res, candidateId, siblingError = null) => {
       const positionId = req.body.position_id;
       
       if (positionId) {
           const mappingData = {
               candidate_id: candidateId,
               position_id: positionId,
               created_by: req.session.userId
           };
           
           Interview.assignCandidateToPosition(mappingData, (mappingErr) => {
               if (mappingErr) {
                   console.error('Error assigning candidate to position:', mappingErr);
               }
               
               res.status(201).json({
                   success: true,
                   message: siblingError 
                       ? 'Candidate created but error with siblings' 
                       : 'Candidate added successfully',
                   candidateId: candidateId,
                   siblingError: siblingError ? siblingError.message : null
               });
           });
       } else {
           res.status(201).json({
               success: true,
               message: siblingError 
                   ? 'Candidate created but error with siblings' 
                   : 'Candidate added successfully',
               candidateId: candidateId,
               siblingError: siblingError ? siblingError.message : null
           });
       }
   },

   // Get all candidates
   getAllCandidates: (req, res) => {
       Interview.getAllCandidates((err, results) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error fetching candidates',
                   error: err
               });
           }
           
           res.status(200).json({
               success: true,
               candidates: results
           });
       });
   },

   // Get candidate by ID
   // Add this debugging to your candidateController.js
getCandidateById: (req, res) => {
    const { id } = req.params;
    console.log('Fetching candidate with ID:', id);
    
    Interview.getCandidateById(id, (err, results) => {
        if (err) {
            console.error('Error in getCandidateById:', err);
            return res.status(500).json({
                success: false,
                message: 'Error fetching candidate',
                error: err
            });
        }
        
        console.log('Results from getCandidateById:', results);
        
        if (!results || results.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Candidate not found'
            });
        }
        
        // Add this debug line
        console.log('Candidate basic data:', results[0]);
        
        const candidateData = results[0];
        
        // Get candidate's positions
        Interview.getCandidatePositions(id, (posErr, positions) => {
            if (posErr) {
                console.error('Error fetching candidate positions:', posErr);
                positions = [];
            }
            
            console.log('Candidate positions:', positions);
            
            // Get candidate's siblings
            Interview.getCandidateSiblings(id, (sibErr, siblings) => {
                if (sibErr) {
                    console.error('Error fetching candidate siblings:', sibErr);
                    siblings = [];
                }
                
                console.log('Candidate siblings:', siblings);
                
                // Combine all data
                const candidate = {
                    ...candidateData,
                    positions: positions || [],
                    siblings: siblings || []
                };
                
                console.log('Final candidate object:', candidate);
                
                res.status(200).json({
                    success: true,
                    candidate: candidate
                });
            });
        });
    });
},

   // Update candidate
   updateCandidate: (req, res) => {
       const { id } = req.params;
       
       // Handle file uploads
       let resumeFileName = null;
       let profilePictureFileName = null;
       
       if (req.files) {
           if (req.files.resumeFile) {
               resumeFileName = req.files.resumeFile[0].filename;
           }
           if (req.files.profilePicture) {
               profilePictureFileName = req.files.profilePicture[0].filename;
           }
       }
       
       // Find candidate data before update
       Interview.getCandidateById(id, (fetchErr, results) => {
           if (fetchErr || results.length === 0) {
               return res.status(fetchErr ? 500 : 404).json({
                   success: false,
                   message: fetchErr ? 'Error fetching candidate' : 'Candidate not found',
                   error: fetchErr
               });
           }
           
           const oldData = results[0];
           
           // Prepare update data
           let candidateData = { ...req.body, updated_by: req.session.userId };
           
           // Parse numeric fields
           if (candidateData.genderId) candidateData.genderId = parseInt(candidateData.genderId);
           if (candidateData.bloodGroupId) candidateData.bloodGroupId = parseInt(candidateData.bloodGroupId);
           if (candidateData.departmentId) candidateData.departmentId = parseInt(candidateData.departmentId);
           if (candidateData.designationId) candidateData.designationId = parseInt(candidateData.designationId);
           if (candidateData.employmentTypeId) candidateData.employmentTypeId = parseInt(candidateData.employmentTypeId);
           if (candidateData.yearOfPassing) candidateData.yearOfPassing = parseInt(candidateData.yearOfPassing);
           
           // Format dates
           if (candidateData.dateOfBirth) {
               candidateData.dateOfBirth = new Date(candidateData.dateOfBirth).toISOString().split('T')[0];
           }
           
           if (candidateData.hireDate) {
               candidateData.hireDate = new Date(candidateData.hireDate).toISOString().split('T')[0];
           }
           
           // Only add file fields if new files were uploaded
           if (resumeFileName) {
               candidateData.resumeFile = resumeFileName;
           }
           
           if (profilePictureFileName) {
               candidateData.profilePicture = profilePictureFileName;
           }
           
           // Update candidate record
           Interview.updateCandidate(id, candidateData, (updateErr, updateResult) => {
               if (updateErr) {
                   // Clean up new files if there was an error
                   if (resumeFileName) {
                       const filePath = path.join(__dirname, '../../uploads', resumeFileName);
                       deleteFile(filePath);
                   }
                   
                   if (profilePictureFileName) {
                       const filePath = path.join(__dirname, '../../uploads', profilePictureFileName);
                       deleteFile(filePath);
                   }
                   
                   return res.status(500).json({
                       success: false,
                       message: 'Error updating candidate',
                       error: updateErr
                   });
               }
               
               // Delete old files if new ones were uploaded
               if (resumeFileName && oldData.resumeFile) {
                   const filePath = path.join(__dirname, '../../uploads', oldData.resumeFile);
                   deleteFile(filePath);
               }
               
               if (profilePictureFileName && oldData.profilePicture) {
                   const filePath = path.join(__dirname, '../../uploads', oldData.profilePicture);
                   deleteFile(filePath);
               }
               
               // Handle siblings if provided
               if (req.body.siblings) {
                   let siblings;
                   try {
                       siblings = typeof req.body.siblings === 'string' 
                           ? JSON.parse(req.body.siblings) 
                           : req.body.siblings;
                   } catch (e) {
                       console.error('Error parsing siblings:', e);
                       return res.status(200).json({
                           success: true,
                           message: 'Candidate updated but error parsing siblings data',
                           candidateId: id
                       });
                   }
                   
                   if (Array.isArray(siblings) && siblings.length > 0) {
                       const siblingPromises = siblings.map(sibling => {
                           return new Promise((resolve, reject) => {
                               if (sibling.id) {
                                   // Update existing sibling
                                   const siblingData = {
                                       name: sibling.name,
                                       date_of_birth: sibling.date_of_birth ? new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                       gender: sibling.gender,
                                       occupation: sibling.occupation || null,
                                       marital_status: sibling.marital_status || 'Single',
                                       contact: sibling.contact || null,
                                       status: sibling.status || 'Living',
                                       is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                       additional_info: sibling.additional_info || null,
                                       updated_by: req.session.userId
                                   };
                                   
                                   Interview.updateCandidateSibling(sibling.id, siblingData, (err, result) => {
                                       if (err) reject(err);
                                       else resolve(result);
                                   });
                               } else {
                                   // Create new sibling
                                   const siblingData = {
                                       candidate_id: id,
                                       name: sibling.name,
                                       date_of_birth: sibling.date_of_birth ? new Date(sibling.date_of_birth).toISOString().split('T')[0] : null,
                                       gender: sibling.gender,
                                       occupation: sibling.occupation || null,
                                       marital_status: sibling.marital_status || 'Single',
                                       contact: sibling.contact || null,
                                       status: sibling.status || 'Living',
                                       is_emergency_contact: sibling.is_emergency_contact ? 1 : 0,
                                       additional_info: sibling.additional_info || null,
                                       created_by: req.session.userId
                                   };
                                   
                                   Interview.createCandidateSibling(siblingData, (err, result) => {
                                       if (err) reject(err);
                                       else resolve(result);
                                   });
                               }
                           });
                       });
                       
                       Promise.all(siblingPromises)
                           .then(() => {
                               res.status(200).json({
                                   success: true,
                                   message: 'Candidate and siblings updated successfully',
                                   candidateId: id
                               });
                           })
                           .catch(error => {
                               console.error('Error updating siblings:', error);
                               res.status(200).json({
                                   success: true,
                                   message: 'Candidate updated but error with siblings',
                                   candidateId: id,
                                   siblingError: error.message
                               });
                           });
                   } else {
                       res.status(200).json({
                           success: true,
                           message: 'Candidate updated successfully',
                           candidateId: id
                       });
                   }
               } else {
                   res.status(200).json({
                       success: true,
                       message: 'Candidate updated successfully',
                       candidateId: id
                   });
               }
           });
       });
   },

   // Delete candidate
   deleteCandidate: (req, res) => {
       const { id } = req.params;
       
       // Find candidate to get file paths before deleting
       Interview.getCandidateById(id, (err, results) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error fetching candidate',
                   error: err
               });
           }
           
           if (results.length === 0) {
               return res.status(404).json({
                   success: false,
                   message: 'Candidate not found'
               });
           }
           
           const resumeFile = results[0].resumeFile;
           const profilePicture = results[0].profilePicture;
           
           // Delete the candidate from the database
           Interview.deleteCandidate(id, (delErr, result) => {
               if (delErr) {
                   return res.status(500).json({
                       success: false,
                       message: 'Error deleting candidate',
                       error: delErr
                   });
               }
               
               // Delete associated files
               if (resumeFile) {
                   const filePath = path.join(__dirname, '../../uploads', resumeFile);
                   deleteFile(filePath);
               }
               
               if (profilePicture) {
                   const filePath = path.join(__dirname, '../../uploads', profilePicture);
                   deleteFile(filePath);
               }
               
               res.status(200).json({
                   success: true,
                   message: 'Candidate deleted successfully'
               });
           });
       });
   },

   // Delete multiple candidates
   deleteCandidates: (req, res) => {
       const { candidateIds } = req.body;
       
       if (!Array.isArray(candidateIds) || candidateIds.length === 0) {
           return res.status(400).json({
               success: false,
               message: 'Invalid candidate IDs'
           });
       }
       
       // Get all candidate information to find files to delete
       const promises = candidateIds.map(id => {
           return new Promise((resolve, reject) => {
               Interview.getCandidateById(id, (err, results) => {
                   if (err || results.length === 0) {
                       resolve({ resumeFile: null, profilePicture: null });
                   } else {
                       resolve({
                           resumeFile: results[0].resumeFile,
                           profilePicture: results[0].profilePicture
                       });
                   }
               });
           });
       });
       
       Promise.all(promises).then(filesInfo => {
           // Delete candidates from the database
           Interview.deleteCandidates(candidateIds, (err) => {
               if (err) {
                   return res.status(500).json({
                       success: false,
                       message: 'Error deleting candidates',
                       error: err
                   });
               }
               
               // Delete files
               filesInfo.forEach(files => {
                   if (files.resumeFile) {
                       const filePath = path.join(__dirname, '../../uploads', files.resumeFile);
                       deleteFile(filePath);
                   }
                   
                   if (files.profilePicture) {
                       const filePath = path.join(__dirname, '../../uploads', files.profilePicture);
                       deleteFile(filePath);
                   }
               });
               
               res.status(200).json({
                   success: true,
                   message: 'Candidates deleted successfully'
               });
           });
       });
   },

   // Update candidate status
   updateCandidateStatus: (req, res) => {
       const { id } = req.params;
       const { status } = req.body;
       const updated_by = req.session.userId;
       
       Interview.updateCandidateStatus(id, status, updated_by, (err, result) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error updating candidate status',
                   error: err
               });
           }
           
           if (result.affectedRows === 0) {
               return res.status(404).json({
                   success: false,
                   message: 'Candidate not found'
               });
           }
           
           res.status(200).json({
               success: true,
               message: 'Candidate status updated successfully'
           });
       });
   },

   // Position Mapping Methods
   assignCandidateToPosition: (req, res) => {
       const { candidate_id, position_id } = req.body;
       
       const mappingData = {
           candidate_id,
           position_id,
           created_by: req.session.userId
       };
       
       Interview.assignCandidateToPosition(mappingData, (err, result) => {
           if (err) {
               // Handle duplicate entry error
               if (err.code === 'ER_DUP_ENTRY') {
                   return res.status(400).json({
                       success: false,
                       message: 'Candidate is already assigned to this position'
                   });
               }
               
               return res.status(500).json({
                   success: false,
                   message: 'Error assigning candidate to position',
                   error: err
               });
           }
           
           res.status(201).json({
               success: true,
               message: 'Candidate assigned to position successfully',
               mappingId: result.insertId
           });
       });
   },

   getCandidatePositions: (req, res) => {
       const { candidateId } = req.params;
       
       Interview.getCandidatePositions(candidateId, (err, results) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error fetching candidate positions',
                   error: err
               });
           }
           
           res.status(200).json({
               success: true,
               positions: results
           });
       });
   },

   getPositionCandidates: (req, res) => {
       const { positionId } = req.params;
       
       Interview.getPositionCandidates(positionId, (err, results) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error fetching position candidates',
                   error: err
               });
           }
           
           res.status(200).json({
               success: true,
               candidates: results
           });
       });
   },

   updateCandidatePositionStatus: (req, res) => {
       const { candidateId, positionId } = req.params;
       const { status } = req.body;
       
       Interview.updateCandidatePositionStatus(candidateId, positionId, status, (err, result) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error updating candidate position status',
                   error: err
               });
           }
           
           if (result.affectedRows === 0) {
               return res.status(404).json({
                   success: false,
                   message: 'Candidate position mapping not found'
               });
           }
           
           res.status(200).json({
               success: true,
               message: 'Candidate position status updated successfully'
           });
       });
   },

   removeCandidateFromPosition: (req, res) => {
       const { candidateId, positionId } = req.params;
       
       Interview.removeCandidateFromPosition(candidateId, positionId, (err, result) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error removing candidate from position',
                   error: err
               });
           }
           
           if (result.affectedRows === 0) {
               return res.status(404).json({
                   success: false,
                   message: 'Candidate position mapping not found'
               });
           }
           
           res.status(200).json({
               success: true,
               message: 'Candidate removed from position successfully'
           });
       });
   },

   // Search
   searchCandidates: (req, res) => {
       const searchTerms = req.body;
       
       Interview.searchCandidates(searchTerms, (err, results) => {
           if (err) {
               return res.status(500).json({
                   success: false,
                   message: 'Error searching candidates',
                   error: err
               });
           }
           
           res.status(200).json({
               success: true,
               candidates: results
           });
       });
   },

   // Recent candidates for dashboard
   getRecentCandidates: (req, res) => {
       const limit = req.query.limit ? parseInt(req.query.limit) : 5;
       
       Interview.getRecentCandidates(limit, (err, results) => {
           if (err) {
               console.error('Error fetching recent candidates:', err);
               return res.status(500).json({
                   success: false,
                   message: 'Error fetching recent candidates',
                   error: err
               });
           }
           
           res.status(200).json({
               success: true,
               candidates: results || []
           });
       });
   }
};

module.exports = candidateController;