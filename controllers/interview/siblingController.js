// controllers/interview/siblingController.js

const Interview = require('../../models/interviewModel');

const siblingController = {
    // Add a sibling for a candidate
    addSibling: (req, res) => {
        const siblingData = {
            candidate_id: req.params.candidateId,
            name: req.body.name,
            date_of_birth: req.body.date_of_birth ? new Date(req.body.date_of_birth).toISOString().split('T')[0] : null,
            gender: req.body.gender,
            occupation: req.body.occupation || null,
            marital_status: req.body.marital_status || 'Single',
            contact: req.body.contact || null,
            status: req.body.status || 'Living',
            is_emergency_contact: req.body.is_emergency_contact ? 1 : 0,
            additional_info: req.body.additional_info || null,
            created_by: req.session.userId
        };
        
        Interview.createCandidateSibling(siblingData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error adding sibling',
                    error: err
                });
            }
            
            res.status(201).json({
                success: true,
                message: 'Sibling added successfully',
                siblingId: result.insertId
            });
        });
    },

    // Get all siblings for a candidate
    getSiblings: (req, res) => {
        const candidateId = req.params.candidateId;
        
        Interview.getCandidateSiblings(candidateId, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching siblings',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                siblings: results
            });
        });
    },

    // Get a specific sibling by ID
    getSiblingById: (req, res) => {
        const { id } = req.params;
        
        Interview.getCandidateSiblingById(id, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching sibling',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Sibling not found'
                });
            }
            
            res.status(200).json({
                success: true,
                sibling: results[0]
            });
        });
    },

    // Update a sibling
    updateSibling: (req, res) => {
        const { id } = req.params;
        
        const siblingData = {
            name: req.body.name,
            date_of_birth: req.body.date_of_birth ? new Date(req.body.date_of_birth).toISOString().split('T')[0] : null,
            gender: req.body.gender,
            occupation: req.body.occupation || null,
            marital_status: req.body.marital_status || 'Single',
            contact: req.body.contact || null,
            status: req.body.status || 'Living',
            is_emergency_contact: req.body.is_emergency_contact ? 1 : 0,
            additional_info: req.body.additional_info || null,
            updated_by: req.session.userId
        };
        
        Interview.updateCandidateSibling(id, siblingData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error updating sibling',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Sibling not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Sibling updated successfully'
            });
        });
    },

    // Delete a sibling
    deleteSibling: (req, res) => {
        const { id } = req.params;
        
        Interview.deleteCandidateSibling(id, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting sibling',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Sibling not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Sibling deleted successfully'
            });
        });
    },

    // Delete multiple siblings
    deleteSiblings: (req, res) => {
        const { siblingIds } = req.body;
        
        if (!Array.isArray(siblingIds) || siblingIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid sibling IDs'
            });
        }
        
        Interview.deleteCandidateSiblings(siblingIds, (err) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting siblings',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Siblings deleted successfully'
            });
        });
    }
};

module.exports = siblingController;