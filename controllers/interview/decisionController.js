// controllers/interview/decisionController.js

const Interview = require('../../models/interviewModel');

const decisionController = {
    // Add a candidate decision
    addCandidateDecision: (req, res) => {
        const decisionData = {
            ...req.body,
            approved_by: req.session.userId
        };
        
        Interview.createCandidateDecision(decisionData, (err, result) => {
            if (err) {
                // Handle duplicate entry error
                if (err.code === 'ER_DUP_ENTRY') {
                    return res.status(400).json({
                        success: false,
                        message: 'A decision for this candidate and position already exists'
                    });
                }
                
                return res.status(500).json({
                    success: false,
                    message: 'Error adding candidate decision',
                    error: err
                });
            }
            
            res.status(201).json({
                success: true,
                message: 'Candidate decision added successfully',
                decisionId: result.insertId
            });
        });
    },

    // Get a candidate decision
    getCandidateDecision: (req, res) => {
        const { candidateId, positionId } = req.params;
        
        Interview.getCandidateDecision(candidateId, positionId, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching candidate decision',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Candidate decision not found'
                });
            }
            
            res.status(200).json({
                success: true,
                decision: results[0]
            });
        });
    },

    // Update a candidate decision
    updateCandidateDecision: (req, res) => {
        const { id } = req.params;
        const decisionData = {
            ...req.body,
            approved_by: req.session.userId
        };
        
        Interview.updateCandidateDecision(id, decisionData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error updating candidate decision',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Candidate decision not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Candidate decision updated successfully'
            });
        });
    },

    // Set staff for a selected candidate
    setStaffForCandidate: (req, res) => {
        const { decisionId } = req.params;
        const { staffId } = req.body;
        
        Interview.setStaffForCandidate(decisionId, staffId, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error setting staff for candidate',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Candidate decision not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Staff assigned to candidate successfully'
            });
        });
    }
};

module.exports = decisionController;