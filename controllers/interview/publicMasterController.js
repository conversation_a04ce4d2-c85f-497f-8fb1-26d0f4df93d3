// controllers/interview/publicMasterController.js
const PublicMasterModel = require('../../models/interview/publicMasterModel');

const PublicMasterController = {
  /**
   * Test database connection - diagnostic route
   */
  testDatabase: (req, res) => {
    try {
      PublicMasterModel.testDatabaseConnection((err, results) => {
        if (err) {
          console.error('Database test failed:', err);
          return res.status(500).json({
            success: false,
            message: 'Database connection test failed',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          message: 'Database connection successful',
          data: results
        });
      });
    } catch (e) {
      console.error('Error in testDatabase controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error testing database',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all genders - public endpoint
   */
  getGenders: (req, res) => {
    try {
      console.log('Processing getGenders request');
      PublicMasterModel.getAllGenders((err, results) => {
        if (err) {
          console.error('Error fetching genders:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching genders',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          genders: results || []
        });
      });
    } catch (e) {
      console.error('Error in getGenders controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all blood groups - public endpoint
   */
  getBloodGroups: (req, res) => {
    try {
      console.log('Processing getBloodGroups request');
      PublicMasterModel.getAllBloodGroups((err, results) => {
        if (err) {
          console.error('Error fetching blood groups:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching blood groups',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          bloodGroups: results || []
        });
      });
    } catch (e) {
      console.error('Error in getBloodGroups controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all religions - public endpoint
   */
  getReligions: (req, res) => {
    try {
      console.log('Processing getReligions request');
      PublicMasterModel.getAllReligions((err, results) => {
        if (err) {
          console.error('Error fetching religions:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching religions',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          religions: results || []
        });
      });
    } catch (e) {
      console.error('Error in getReligions controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all communities - public endpoint
   */
  getCommunities: (req, res) => {
    try {
      console.log('Processing getCommunities request');
      PublicMasterModel.getAllCommunities((err, results) => {
        if (err) {
          console.error('Error fetching communities:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching communities',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          communities: results || []
        });
      });
    } catch (e) {
      console.error('Error in getCommunities controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all states - public endpoint
   */
  getStates: (req, res) => {
    try {
      console.log('Processing getStates request');
      PublicMasterModel.getAllStates((err, results) => {
        if (err) {
          console.error('Error fetching states:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching states',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          states: results || []
        });
      });
    } catch (e) {
      console.error('Error in getStates controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get cities by state ID - public endpoint
   */
  getCitiesByState: (req, res) => {
    try {
      const stateId = req.params.stateId;
      console.log(`Processing getCitiesByState request for state ID: ${stateId}`);
      
      if (!stateId) {
        return res.status(400).json({
          success: false,
          message: 'State ID is required'
        });
      }
      
      PublicMasterModel.getCitiesByStateId(stateId, (err, results) => {
        if (err) {
          console.error('Error fetching cities:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching cities',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          cities: results || []
        });
      });
    } catch (e) {
      console.error('Error in getCitiesByState controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get open positions - public endpoint
   */
  getOpenPositions: (req, res) => {
    try {
      console.log('Processing getOpenPositions request');
      PublicMasterModel.getOpenPositions((err, results) => {
        if (err) {
          console.error('Error fetching open positions:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching open positions',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          positions: results || []
        });
      });
    } catch (e) {
      console.error('Error in getOpenPositions controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get position by ID - public endpoint
   */
  getPositionById: (req, res) => {
    try {
      const positionId = req.params.id;
      console.log(`Processing getPositionById request for position ID: ${positionId}`);
      
      if (!positionId) {
        return res.status(400).json({
          success: false,
          message: 'Position ID is required'
        });
      }
      
      PublicMasterModel.getPublicPositionById(positionId, (err, results) => {
        if (err) {
          console.error('Error fetching position:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching position',
            error: err.message || String(err)
          });
        }
        
        if (results.length === 0) {
          return res.status(404).json({
            success: false,
            message: 'Position not found or not available'
          });
        }
        
        res.json({
          success: true,
          position: results[0]
        });
      });
    } catch (e) {
      console.error('Error in getPositionById controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all departments - public endpoint
   */
  getDepartments: (req, res) => {
    try {
      console.log('Processing getDepartments request');
      PublicMasterModel.getAllDepartments((err, results) => {
        if (err) {
          console.error('Error fetching departments:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching departments',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          departments: results || []
        });
      });
    } catch (e) {
      console.error('Error in getDepartments controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all designations - public endpoint
   */
  getDesignations: (req, res) => {
    try {
      console.log('Processing getDesignations request');
      PublicMasterModel.getAllDesignations((err, results) => {
        if (err) {
          console.error('Error fetching designations:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching designations',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          designations: results || []
        });
      });
    } catch (e) {
      console.error('Error in getDesignations controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  },

  /**
   * Get all employment types - public endpoint
   */
  getEmploymentTypes: (req, res) => {
    try {
      console.log('Processing getEmploymentTypes request');
      PublicMasterModel.getAllEmploymentTypes((err, results) => {
        if (err) {
          console.error('Error fetching employment types:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching employment types',
            error: err.message || String(err)
          });
        }
        
        res.json({
          success: true,
          employmentTypes: results || []
        });
      });
    } catch (e) {
      console.error('Error in getEmploymentTypes controller:', e);
      res.status(500).json({
        success: false,
        message: 'Unexpected error processing request',
        error: e.message || String(e)
      });
    }
  }
};

module.exports = PublicMasterController;