// controllers/interview/positionController.js

const Interview = require('../../models/interviewModel');

const positionController = {
    // Add a position
    addPosition: (req, res) => {
        const positionData = {
            ...req.body,
            created_by: req.session.userId,
            updated_by: req.session.userId
        };

        Interview.createPosition(positionData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error adding position',
                    error: err
                });
            }
            
            res.status(201).json({
                success: true,
                message: 'Position added successfully',
                positionId: result.insertId
            });
        });
    },

    // Get all positions
    getAllPositions: (req, res) => {
        Interview.getAllPositions((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching positions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                positions: results
            });
        });
    },

    // Get position by ID
    getPositionById: (req, res) => {
        const { id } = req.params;
        
        Interview.getPositionById(id, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching position',
                    error: err
                });
            }
            
            if (results.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Position not found'
                });
            }
            
            res.status(200).json({
                success: true,
                position: results[0]
            });
        });
    },

    // Update position
    updatePosition: (req, res) => {
        const { id } = req.params;
        const positionData = {
            ...req.body,
            updated_by: req.session.userId
        };
        
        Interview.updatePosition(id, positionData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error updating position',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Position not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Position updated successfully'
            });
        });
    },

    // Delete position
    deletePosition: (req, res) => {
        const { id } = req.params;
        
        Interview.deletePosition(id, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting position',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Position not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Position deleted successfully'
            });
        });
    },

    // Delete multiple positions
    deletePositions: (req, res) => {
        const { positionIds } = req.body;
        
        if (!Array.isArray(positionIds) || positionIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Invalid position IDs'
            });
        }
        
        Interview.deletePositions(positionIds, (err) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting positions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Positions deleted successfully'
            });
        });
    },

    // Toggle position status
    togglePositionStatus: (req, res) => {
        const { id } = req.params;
        const updated_by = req.session.userId;
        
        Interview.togglePositionStatus(id, updated_by, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error toggling position status',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Position not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Position status updated successfully'
            });
        });
    },

    // Update vacancies
    updateVacancies: (req, res) => {
        const { id } = req.params;
        const { vacancies } = req.body;
        const updated_by = req.session.userId;
        
        Interview.updateVacancies(id, vacancies, updated_by, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error updating vacancies',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Position not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Vacancies updated successfully'
            });
        });
    },

    // Get positions by department
    getPositionsByDepartment: (req, res) => {
        const { departmentId } = req.params;
        
        Interview.getPositionsByDepartment(departmentId, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching positions by department',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                positions: results
            });
        });
    },

    // Get open positions
    getOpenPositions: (req, res) => {
        Interview.getOpenPositions((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching open positions',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                positions: results
            });
        });
    }
};

module.exports = positionController;