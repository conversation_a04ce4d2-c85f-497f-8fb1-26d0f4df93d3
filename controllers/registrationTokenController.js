// controllers/registrationTokenController.js

const RegistrationToken = require('../models/registrationTokenModel');

const registrationTokenController = {
    // Generate a new registration token
    generateToken: (req, res) => {
        const tokenData = {
            ...req.body,
            created_by: req.session.userId
        };
        
        RegistrationToken.generateToken(tokenData, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error generating registration token',
                    error: err
                });
            }
            
            // Get the generated token
            RegistrationToken.validateToken(tokenData.token, (validateErr, tokenResult) => {
                if (validateErr || tokenResult.length === 0) {
                    return res.status(201).json({
                        success: true,
                        message: 'Registration token generated successfully',
                        tokenId: result.insertId
                    });
                }
                
                res.status(201).json({
                    success: true,
                    message: 'Registration token generated successfully',
                    token: tokenResult[0]
                });
            });
        });
    },
    
    // Get all registration tokens
    getAllTokens: (req, res) => {
        RegistrationToken.getAllTokens((err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching registration tokens',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                tokens: results
            });
        });
    },
    
    // Get tokens created by the current user
    getMyTokens: (req, res) => {
        const userId = req.session.userId;
        
        RegistrationToken.getTokensByUser(userId, (err, results) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching your registration tokens',
                    error: err
                });
            }
            
            res.status(200).json({
                success: true,
                tokens: results
            });
        });
    },
    
    // Invalidate a token
    invalidateToken: (req, res) => {
        const { token } = req.params;
        
        RegistrationToken.invalidateToken(token, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error invalidating token',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Token not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Token invalidated successfully'
            });
        });
    },
    
    // Delete a token
    deleteToken: (req, res) => {
        const { id } = req.params;
        
        RegistrationToken.deleteToken(id, (err, result) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error deleting token',
                    error: err
                });
            }
            
            if (result.affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Token not found'
                });
            }
            
            res.status(200).json({
                success: true,
                message: 'Token deleted successfully'
            });
        });
    }
};

module.exports = registrationTokenController;
