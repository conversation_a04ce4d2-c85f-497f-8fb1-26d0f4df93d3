const Attendance = require('../models/attendanceModel');

const attendanceController = {
    // Mark attendance for a store
    markAttendance: (req, res) => {
        const { store_id, date, attendance_records } = req.body;
        const created_by = req.session.userId;

        // Validate required fields
        if (!store_id || !date || !attendance_records || !Array.isArray(attendance_records)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid request data. Required fields: store_id, date, and attendance_records array'
            });
        }

        // Format attendance records for bulk insert/update
        const formattedRecords = attendance_records.map(record => [
            record.staff_id,
            store_id,
            date,
            record.status,
            record.status === 'absent' ? record.absence_reason : null,
            created_by
        ]);

        Attendance.markAttendance(formattedRecords, (err, result) => {
            if (err) {
                console.error('Error marking attendance:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error marking attendance',
                    error: err
                });
            }

            res.status(200).json({
                success: true,
                message: 'Attendance marked successfully',
                result: result
            });
        });
    },

    // Get attendance for a store on a specific date
    getStoreAttendance: (req, res) => {
        const { store_id, date } = req.params;

        if (!store_id || !date) {
            return res.status(400).json({
                success: false,
                message: 'Store ID and date are required'
            });
        }

        Attendance.getAttendanceByStoreAndDate(store_id, date, (err, results) => {
            if (err) {
                console.error('Error fetching attendance:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching attendance',
                    error: err
                });
            }

            res.status(200).json({
                success: true,
                attendance: results
            });
        });
    },

    // Get attendance history for a staff member
    getStaffHistory: (req, res) => {
        const { staff_id } = req.params;
        const { start_date, end_date } = req.query;

        if (!staff_id || !start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: 'Staff ID, start date, and end date are required'
            });
        }

        Attendance.getStaffAttendanceHistory(staff_id, start_date, end_date, (err, results) => {
            if (err) {
                console.error('Error fetching attendance history:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching attendance history',
                    error: err
                });
            }

            res.status(200).json({
                success: true,
                history: results
            });
        });
    },

    // Get attendance summary for a store
    getStoreSummary: (req, res) => {
        const { store_id } = req.params;
        const { start_date, end_date } = req.query;

        if (!store_id || !start_date || !end_date) {
            return res.status(400).json({
                success: false,
                message: 'Store ID, start date, and end date are required'
            });
        }

        Attendance.getStoreSummary(store_id, start_date, end_date, (err, results) => {
            if (err) {
                console.error('Error fetching store summary:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching store summary',
                    error: err
                });
            }

            res.status(200).json({
                success: true,
                summary: results
            });
        });
    }
};

module.exports = attendanceController;