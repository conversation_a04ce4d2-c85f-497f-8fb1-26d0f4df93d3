const bcrypt = require('bcrypt');
const saltRounds = 10;
const User = require('../models/userModel');
const Role = require('../models/roleModel');
const Auth = require('../models/authModel');
const { generateOtp, verifyOtp } = require('../services/otpService');
const db = require('../config/database'); 

// Login function
const login = (req, res) => {
    const { identifier, password, otp } = req.body;
    const isEmail = identifier.includes('@');
    const isMobile = /^\d+$/.test(identifier);
    const loginType = isEmail ? 'email' : (isMobile ? 'mobile' : 'username');

    console.log('Login attempt:', { identifier, loginType });

    if (otp) {
        handleOtpLogin(identifier, otp, req, res);
    } else {
        handlePasswordLogin(loginType, identifier, password, req, res);
    }
};

const handleOtpLogin = (identifier, otp, req, res) => {
    if (!/^\d+$/.test(identifier) || !verifyOtp(identifier, otp)) {
        return res.status(401).json({ success: false, message: 'Invalid OTP or mobile number' });
    }

    User.findUserByMobileNumber(identifier, (err, user) => {
        handleLoginResult(req, res, err, user);
    });
};

const handlePasswordLogin = (loginType, identifier, password, req, res) => {
    const findUserFunction = {
        email: User.findUserByEmailAndPassword,
        mobile: User.findUserByMobileNumberAndPassword,
        username: User.findUserByUsernameAndPassword
    }[loginType];

    findUserFunction(identifier, password, (err, user) => {
        if (user) {
            bcrypt.compare(password, user.password, (compareErr, isMatch) => {
                console.log(`Password match for ${loginType}:`, isMatch);
                if (compareErr || !isMatch) {
                    err = 'Invalid credentials';
                    user = null;
                }
                handleLoginResult(req, res, err, user);
            });
        } else {
            handleLoginResult(req, res, err, user);
        }
    });
};

const handleLoginResult = (req, res, err, user) => {
    if (err || !user) {
        console.log('Login failed. Invalid credentials.');
        return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    if (!user.is_active) {
        console.log('Login failed. Account is inactive:', user.username);
        return res.status(403).json({ success: false, message: 'Account is inactive' });
    }

    req.session.userId = user.id;
    req.session.userRole = user.role_id;
    req.session.username = user.username; // Store username in session
    
    console.log('===== User logged in =====');
    console.log('User ID:', user.id);
    console.log('Username:', user.username);
    console.log('Email:', user.email);
    console.log('Role:', user.role_id);
    console.log('========================');

    res.json({
        success: true,
        message: 'Login successful',
        user: {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role_id
        }
    });
};

// Send OTP to mobile number
const sendOtp = (req, res) => {
    const { mobile_number } = req.body;

    User.findUserByMobileNumber(mobile_number, (err, user) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred during sending OTP', error: err });
        }
        if (user) {
            const otp = generateOtp(mobile_number);
            console.log(`OTP for ${mobile_number}: ${otp}`);
            return res.json({ success: true, message: 'OTP sent successfully' });
        } else {
            return res.status(401).json({ success: false, message: 'User not found' });
        }
    });
};

// Logout function
const logout = (req, res) => {
    // Log who is logging out
    if (req.session && req.session.userId) {
        console.log('===== User logging out =====');
        console.log('User ID:', req.session.userId);
        console.log('Username:', req.session.username || 'Unknown');
        console.log('===========================');
    }
    
    req.session.destroy((err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred during logout', error: err });
        } else {
            res.clearCookie('connect.sid');
            return res.json({ success: true, message: 'Logout successful' });
        }
    });
};

// Add user function
const addUser = (req, res) => {
    const { username, password, role_id, mobile_number, email, is_active } = req.body;
    const created_by = req.session.userId;
    
    console.log('User being created by:', {
        creatorId: created_by,
        creatorUsername: req.session.username || 'Unknown'
    });

    bcrypt.hash(password, saltRounds, (err, hashedPassword) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred during password hashing', error: err.message });
        }

        User.createUser({ username, password: hashedPassword, role_id, mobile_number, email, is_active, created_by }, (err, results) => {
            if (err) {
                return res.status(400).json({ success: false, message: err });
            }
            res.status(201).json({ success: true, message: 'User created successfully', userId: results.insertId });
        });
    });
};

// Get user info function
const getUser = (req, res) => {
    if (req.session.userId) {
        console.log('Getting user info for:', {
            userId: req.session.userId,
            username: req.session.username || 'Unknown'
        });
        
        res.json({
            success: true,
            user: {
                id: req.session.userId,
                username: req.session.username || 'Unknown',
                role: req.session.userRole
            }
        });
    } else {
        console.log('Attempted to get user info with no session');
        res.status(401).json({ success: false, message: 'No user logged in' });
    }
};

const getUserById = (req, res) => {
    const userId = req.params.id;
    
    console.log('Getting info for user ID:', userId, 'requested by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown'
    });

    User.findById(userId, (err, user) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while fetching user', error: err });
        }
        if (user) {
            return res.json({ success: true, user: { id: user.id, username: user.username, email: user.email, role_id: user.role_id } });
        } else {
            return res.status(404).json({ success: false, message: 'User not found' });
        }
    });
};

const validateSession = (req, res) => {
    if (!req.session || !req.session.userId) {
        console.log('Session validation failed: No active session');
        return res.status(401).json({ success: false, message: 'No active session' });
    }
    
    console.log('Validating session for:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown'
    });

    Auth.validateSession(req.session.userId, (err, result) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error validating session' });
        }
        res.json({ success: true, isValid: true });
    });
};

const checkPermissions = (req, res) => {
    const { module, action } = req.query;
    const roleId = req.session.userRole;

    console.log(`Checking permissions for user:`, {
        userId: req.session.userId,
        username: req.session.username || 'Unknown',
        roleId: roleId,
        module: module,
        action: action
    });

    if (!module || !action) {
        return res.status(400).json({ success: false, message: 'Module and action are required' });
    }

    if (!roleId) {
        return res.status(401).json({ success: false, message: 'Unauthorized: No role found for user' });
    }

    const query = `
        SELECT rmp.permissions
        FROM role_module_permissions rmp
        JOIN modules m ON rmp.module_id = m.id
        WHERE rmp.role_id = ? AND m.name = ?
    `;
    
    db.query(query, [roleId, module], (err, results) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ success: false, message: 'Error checking permissions', error: err.message });
        }
        if (results.length === 0) {
            console.log('No permissions found');
            return res.status(403).json({ success: false, message: 'Forbidden: Insufficient permissions' });
        }
        const permissions = results[0].permissions;
        console.log('Permissions found:', permissions);
        if (permissions.includes(action)) {
            console.log(`Permission granted for action: ${action}`);
            return res.json({ success: true, message: 'Permission granted', hasPermission: true });
        } else {
            console.log(`Permission denied for action: ${action}`);
            return res.status(403).json({ success: false, message: 'Forbidden: Insufficient permissions', hasPermission: false });
        }
    });
};

// Add role function
const addRole = (req, res) => {
    const { name, permissions } = req.body;
    
    console.log('Adding role by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown'
    });

    // Check if the role already exists
    Role.findByName(name, (err, existingRole) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while checking for existing role', error: err });
        }

        if (existingRole) {
            return res.status(400).json({ success: false, message: 'Role already exists' });
        }

        // If the role does not exist, create a new one
        Role.create({ name, permissions }, (err, results) => {
            if (err) {
                return res.status(500).json({ success: false, message: 'Error occurred during role creation', error: err });
            }
            res.status(201).json({ success: true, role: results.insertId });
        });
    });
};

// Get roles function
const getRoles = (req, res) => {
    console.log('Getting roles, requested by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown'
    });
    
    Role.findAll((err, roles) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while fetching roles', error: err });
        }
        res.json({ success: true, roles });
    });
};

// Update role function
const updateRole = (req, res) => {
    const roleId = req.params.id;
    const { name, permissions } = req.body;
    
    console.log('Updating role, by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown',
        roleId: roleId
    });
    
    Role.update(roleId, { name, permissions }, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while updating role', error: err });
        }
        res.json({ success: true, message: 'Role updated successfully' });
    });
};

// Delete role function
const deleteRole = (req, res) => {
    const roleId = req.params.id;
    
    console.log('Deleting role, by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown',
        roleId: roleId
    });
    
    Role.delete(roleId, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while deleting role', error: err });
        }
        res.json({ success: true, message: 'Role deleted successfully' });
    });
};

// Get all users function
const getAllUsers = (req, res) => {
    console.log('Getting all users, requested by:', {
        userId: req.session.userId,
        username: req.session.username || 'Unknown'
    });
    
    User.getAllUsers((err, users) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while fetching users', error: err });
        }
        res.json({ success: true, users });
    });
};

// Update user function
const updateUser = (req, res) => {
    const userId = req.params.id;
    const { username, role_id, mobile_number, email, is_active } = req.body;
    const updated_by = req.session.userId;
    
    console.log('Updating user, by:', {
        updaterId: updated_by,
        updaterUsername: req.session.username || 'Unknown',
        targetUserId: userId
    });

    User.update({ id: userId, username, role_id, mobile_number, email, is_active, updated_by }, (err, result) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while updating user', error: err });
        }
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }
        res.json({ success: true, message: 'User updated successfully' });
    });
};

// Delete user function
const deleteUser = (req, res) => {
    const userId = req.params.id;
    
    console.log('Deleting user, by:', {
        deleterId: req.session.userId,
        deleterUsername: req.session.username || 'Unknown',
        targetUserId: userId
    });

    User.delete(userId, (err, result) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while deleting user', error: err });
        }
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }
        res.json({ success: true, message: 'User deleted successfully' });
    });
};

// Toggle user active status function
const toggleUserActiveStatus = (req, res) => {
    const userId = req.params.id;
    const updated_by = req.session.userId;
    
    console.log('Toggling user status, by:', {
        updaterId: updated_by,
        updaterUsername: req.session.username || 'Unknown',
        targetUserId: userId
    });

    User.toggleActiveStatus(userId, updated_by, (err, result) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error occurred while toggling user status', error: err });
        }
        if (result.affectedRows === 0) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }
        res.json({ success: true, message: 'User status toggled successfully' });
    });
};

// Current user debug endpoint
const getCurrentUser = (req, res) => {
    if (req.session && req.session.userId) {
        User.findById(req.session.userId, (err, user) => {
            if (err || !user) {
                console.log('Error fetching current user:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Error fetching user data',
                    session: {
                        userId: req.session.userId,
                        username: req.session.username,
                        userRole: req.session.userRole
                    }
                });
            }
            
            console.log('Current user info:', {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role_id
            });
            
            res.json({
                success: true,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role_id
                },
                session: {
                    userId: req.session.userId,
                    username: req.session.username,
                    userRole: req.session.userRole
                }
            });
        });
    } else {
        console.log('No user is logged in');
        res.status(401).json({
            success: false,
            message: 'No user logged in',
            session: req.session || 'No session'
        });
    }
};

module.exports = {
    login,
    sendOtp,
    logout,
    addUser,
    getUser,
    addRole,
    getRoles,
    updateRole,
    deleteRole, 
    getUserById,
    checkPermissions, 
    validateSession,
    getAllUsers,
    updateUser,
    deleteUser,
    toggleUserActiveStatus,
    getCurrentUser  // Added the new debug endpoint
};