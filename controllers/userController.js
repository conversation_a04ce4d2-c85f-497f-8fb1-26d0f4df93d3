const User = require('../models/userModel');
const bcrypt = require('bcrypt');
const saltRounds = 10;

const userController = {
    // Get all users
    getAllUsers: (req, res) => {
        User.getAllUsers((err, users) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error fetching users', 
                    error: err 
                });
            }
            res.json({ success: true, users });
        });
    },

    // Get user by ID
    getUserById: (req, res) => {
        const userId = req.params.id;
        User.getUserById(userId, (err, user) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error fetching user', 
                    error: err 
                });
            }
            res.json({ success: true, user });
        });
    },

    // Add new user
    createUser: (req, res) => {
        const { username, password, role_id, mobile_number, email, is_active } = req.body;
        const created_by = req.session.userId;

        bcrypt.hash(password, saltRounds, (err, hashedPassword) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error hashing password', 
                    error: err 
                });
            }

            User.createUser({
                username,
                password: hashedPassword,
                role_id,
                mobile_number,
                email,
                is_active,
                created_by
            }, (err, result) => {
                if (err) {
                    return res.status(400).json({ 
                        success: false, 
                        message: err 
                    });
                }
                res.status(201).json({ 
                    success: true, 
                    message: 'User created successfully', 
                    userId: result.insertId 
                });
            });
        });
    },

    // Update user
    updateUser: (req, res) => {
        const userId = req.params.id;
        const { username, role_id, mobile_number, email, is_active } = req.body;
        const updated_by = req.session.userId;

        User.updateUser({
            id: userId,
            username,
            role_id,
            mobile_number,
            email,
            is_active,
            updated_by
        }, (err, result) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error updating user', 
                    error: err 
                });
            }
            res.json({ 
                success: true, 
                message: 'User updated successfully' 
            });
        });
    },

    // Delete user
    deleteUser: (req, res) => {
        const userId = req.params.id;
        User.deleteUser(userId, (err, result) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error deleting user', 
                    error: err 
                });
            }
            res.json({ 
                success: true, 
                message: 'User deleted successfully' 
            });
        });
    },


    deleteUsers: (req, res) => {
        const { userIds } = req.body;
        if (!Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({ success: false, message: 'Invalid user IDs' });
        }
    
        User.deleteUsers(userIds, (err) => {
            if (err) {
                return res.status(500).json({ success: false, message: 'Error deleting users', error: err });
            }
            return res.json({ success: true, message: 'Users deleted successfully' });
        });
    },



    // Toggle user active status
    toggleUserStatus: (req, res) => {
        const userId = req.params.id;
        const updated_by = req.session.userId;

        User.getUserById(userId, (err, user) => {
            if (err) {
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error fetching user', 
                    error: err 
                });
            }

            User.updateUser({
                id: userId,
                username: user.username,
                role_id: user.role_id,
                mobile_number: user.mobile_number,
                email: user.email,
                is_active: !user.is_active,
                updated_by
            }, (err, result) => {
                if (err) {
                    return res.status(500).json({ 
                        success: false, 
                        message: 'Error toggling user status', 
                        error: err 
                    });
                }
                res.json({ 
                    success: true, 
                    message: 'User status updated successfully' 
                });
            });
        });
    },

    // Protected route check
    getProtected: (req, res) => {
        if (req.session && req.session.userId) {
            res.json({ message: 'This is a protected route', user: req.session.userId });
        } else {
            res.status(403).send('A valid session is required for authentication');
        }
    }
};

module.exports = userController;