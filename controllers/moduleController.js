const Module = require('../models/moduleModel');

// Create a module
const createModule = (req, res) => {
    const { name } = req.body;

    // Check if the module name already exists
    Module.findByName(name, (err, existingModule) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error checking module name', error: err });
        }
        if (existingModule) {
            return res.status(400).json({ success: false, message: 'Module name already exists' });
        }

        // If the module name does not exist, create a new module
        Module.createModule({ name }, (err, result) => {
            if (err) {
                return res.status(500).json({ success: false, message: 'Error creating module', error: err });
            }
            res.status(201).json({ success: true, message: 'Module created successfully', module: result });
        });
    });
};

// Get all modules
const getModules = (req, res) => {
    Module.getModules((err, results) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error fetching modules', error: err });
        }
        res.json({ success: true, modules: results });
    });
};

// Get permissions for a role
const getRolePermissions = (req, res) => {
    const roleId = req.params.roleId;
    Module.getRolePermissions(roleId, (err, results) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error fetching role permissions', error: err });
        }
        res.json({ success: true, permissions: results });
    });
};

// Set permissions for a role
const setRolePermissions = (req, res) => {
    const { roleId, moduleId, permissions } = req.body;
    Module.setRolePermissions({ roleId, moduleId, permissions }, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error setting role permissions', error: err });
        }
        res.json({ success: true, message: 'Permissions updated successfully' });
    });
};

// Delete a module and its related permissions
const deleteModule = (req, res) => {
    const moduleId = req.params.id;
    Module.deleteModule(moduleId, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error deleting module', error: err });
        }
        res.json({ success: true, message: 'Module deleted successfully' });
    });
};

// Update a module name
const updateModuleName = (req, res) => {
    const { id } = req.params; // Get ID from the URL
    const { name } = req.body;
    Module.updateModuleName(id, name, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error updating module name', error: err });
        }
        res.json({ success: true, message: 'Module name updated successfully' });
    });
};

// Update role module permissions
const updateRoleModulePermissions = (req, res) => {
    const { roleId, moduleId, permissions } = req.body;
    Module.updateRoleModulePermissions({ roleId, moduleId, permissions }, (err) => {
        if (err) {
            return res.status(500).json({ success: false, message: 'Error updating role module permissions', error: err });
        }
        res.json({ success: true, message: 'Role module permissions updated successfully' });
    });
};

module.exports = {
    createModule,
    getModules,
    getRolePermissions,
    setRolePermissions,
    deleteModule,
    updateModuleName,
    updateRoleModulePermissions // Export the updateRoleModulePermissions function
};