const db = require('../config/database');

// Middleware to require authentication
const requireAuth = (req, res, next) => {
    if (req.session && req.session.userId) {
        next();
    } else {
        res.status(403).send('A valid session is required for authentication');
    }
};

// Middleware to check if a session exists
const checkSession = (req, res, next) => {
    if (req.session && req.session.userId) {
        next();
    } else {
        res.status(401).json({ success: false, message: 'Unauthorized' });
    }
};

// Middleware to check specific permissions
const checkPermissions = (moduleName, action) => {
    return (req, res, next) => {
        const roleId = req.session.userRole;
        console.log(req.session.userId);

        console.log(`Checking permissions for role ID: ${roleId}, module: ${moduleName}, action: ${action}`);

        const query = `
            SELECT rmp.permissions
            FROM role_module_permissions rmp
            JOIN modules m ON rmp.module_id = m.id
            WHERE rmp.role_id = ? AND m.name = ?
        `;
        db.query(query, [roleId, moduleName], (err, results) => {
            if (err) {
                console.error('Database error:', err);
                return res.status(500).json({ success: false, message: 'Error checking permissions', error: err });
            }
            if (results.length === 0) {
                console.log('No permissions found');
                return res.status(403).json({ success: false, message: 'Forbidden: Insufficient permissions' });
            }
            const permissions = results[0].permissions;
            console.log('Permissions found:', permissions);
            if (permissions.includes(action)) {
                console.log(`Permission granted for action: ${action}`);
                next();
            } else {
                console.log(`Permission denied for action: ${action}`);
                res.status(403).json({ success: false, message: 'Forbidden: Insufficient permissions' });
            }
        });
    };
};

module.exports = {
    requireAuth,
    checkSession,
    checkPermissions
};