// middlewares/fileUpload.js

const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure uploads directory exists
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage for resume files
const resumeStorage = multer.diskStorage({
    destination: function(req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
        const extension = path.extname(file.originalname);
        const fileName = `resume-${Date.now()}-${Math.round(Math.random() * 1E9)}${extension}`;
        cb(null, fileName);
    }
});

// Configure storage for profile pictures
const profilePictureStorage = multer.diskStorage({
    destination: function(req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
        const extension = path.extname(file.originalname);
        const fileName = `profile-${Date.now()}-${Math.round(Math.random() * 1E9)}${extension}`;
        cb(null, fileName);
    }
});

// Configure storage for any file
const generalStorage = multer.diskStorage({
    destination: function(req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
        const extension = path.extname(file.originalname);
        const fileName = `file-${Date.now()}-${Math.round(Math.random() * 1E9)}${extension}`;
        cb(null, fileName);
    }
});

// Filter for resume files
const resumeFilter = (req, file, cb) => {
    const allowedTypes = /pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
        return cb(null, true);
    } else {
        cb(new Error('Only PDF and Word documents are allowed for resumes!'));
    }
};

// Filter for profile pictures
const profilePictureFilter = (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
        return cb(null, true);
    } else {
        cb(new Error('Only JPG, PNG and GIF images are allowed for profile pictures!'));
    }
};

// File upload configurations
const uploadResumeFile = multer({
    storage: resumeStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: resumeFilter
}).single('resumeFile');

const uploadProfilePicture = multer({
    storage: profilePictureStorage,
    limits: { fileSize: 3 * 1024 * 1024 }, // 3MB limit for images
    fileFilter: profilePictureFilter
}).single('profilePicture');

// Combined upload for both resume and profile picture
const uploadCandidateFiles = multer({
    storage: generalStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (req, file, cb) => {
        if (file.fieldname === 'resumeFile') {
            resumeFilter(req, file, cb);
        } else if (file.fieldname === 'profilePicture') {
            profilePictureFilter(req, file, cb);
        } else {
            cb(new Error('Unexpected field'));
        }
    }
}).fields([
    { name: 'resumeFile', maxCount: 1 },
    { name: 'profilePicture', maxCount: 1 }
]);

// Middleware for handling upload errors
const handleUploadError = (err, req, res, next) => {
    if (err instanceof multer.MulterError) {
        // A Multer error occurred when uploading
        if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: 'File size is too large. Maximum size is 5MB.'
            });
        }
        return res.status(400).json({
            success: false,
            message: `Upload error: ${err.message}`
        });
    } else if (err) {
        // An unknown error occurred
        return res.status(400).json({
            success: false,
            message: err.message
        });
    }
    next();
};

module.exports = {
    uploadResumeFile,
    uploadProfilePicture,
    uploadCandidateFiles,
    handleUploadError
};