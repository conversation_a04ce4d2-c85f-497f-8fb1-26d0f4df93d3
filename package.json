{"name": "bmmapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "build": "npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": "22.1.0"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-mysql-session": "^3.0.3", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.7.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.5"}}