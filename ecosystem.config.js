module.exports = {
  apps: [{
    name: "bawa<PERSON><PERSON><PERSON>",
    script: "app.js",
    // Optimize for current memory usage
    node_args: "--max-old-space-size=256", // Since you're only using ~20MB
    max_memory_restart: "200M",
    
    // Clustering and performance
    instances: "max",
    exec_mode: "cluster",
    
    // Restart settings
    autorestart: true,
    max_restarts: 5,
    restart_delay: 5000,
    min_uptime: "30s",
    
    // Better error handling
    listen_timeout: 8000,
    kill_timeout: 5000,
    
    // Logging
    error_file: "logs/err.log",
    out_file: "logs/out.log",
    log_date_format: "YYYY-MM-DD HH:mm:ss Z",
    merge_logs: true,
    
    // Metrics
    metrics: {
      http: true,
      runtime: true
    },
    
    env: {
      NODE_ENV: "production",
      // Add some GC tuning since memory usage is low
      NODE_OPTIONS: "--max-old-space-size=256 --gc-interval=100"
    }
  }]
}
